#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to generate 1000 problem instances with 5 robots and 30 tasks
Saves makespan, feasibility, and computational time to CSV
"""

import os
import sys
import subprocess
from datetime import datetime

def main():
    """Run the full generation process"""
    print("🚀 Starting generation of 1000 problem instances with 5 robots and 30 tasks")
    print("=" * 80)
    
    start_time = datetime.now()
    
    # Ensure we're in the right directory
    if not os.path.exists("generate_saved_makespan.py"):
        print("❌ Error: generate_saved_makespan.py not found in current directory")
        print("   Please run this script from the MRCScheduling_2_6_30 directory")
        return False
    
    # Check if <PERSON><PERSON><PERSON> is available
    try:
        import gurobipy
        print("✅ Gurobi found - using optimized generation")
    except ImportError:
        print("⚠️  Gurobi not found - generation may be slower or fail")
        print("   Please install <PERSON>urobi for optimal performance")
        return False
    
    # Create output directories
    output_folder = "problem_instances_5r_30t"
    constraints_folder = os.path.join(output_folder, "constraints")
    solutions_folder = os.path.join(output_folder, "solutions")
    
    os.makedirs(constraints_folder, exist_ok=True)
    os.makedirs(solutions_folder, exist_ok=True)
    
    print(f"📁 Output folder: {output_folder}")
    print(f"📁 Constraints folder: {constraints_folder}")
    print(f"📁 Solutions folder: {solutions_folder}")
    print()
    
    # Run the generation script
    print("🔄 Running generate_saved_makespan.py...")
    try:
        result = subprocess.run([
            "python3", "generate_saved_makespan.py"
        ], capture_output=True, text=True, timeout=7200)  # 2 hour timeout
        
        if result.returncode == 0:
            print("✅ Generation completed successfully!")
            print("\n📊 Generation output:")
            print(result.stdout)
            
            if result.stderr:
                print("\n⚠️  Warnings/Errors:")
                print(result.stderr)
        else:
            print("❌ Generation failed!")
            print(f"Return code: {result.returncode}")
            print(f"Error output: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Generation timed out after 2 hours")
        return False
    except Exception as e:
        print(f"❌ Error running generation: {e}")
        return False
    
    end_time = datetime.now()
    total_time = (end_time - start_time).total_seconds()
    
    # Check results
    csv_file = os.path.join(output_folder, "makespans_detailed.csv")
    if os.path.exists(csv_file):
        print(f"\n📈 Results saved to: {csv_file}")
        
        # Count generated files
        constraint_files = len([f for f in os.listdir(constraints_folder) if f.endswith('_dur.txt')])
        solution_files = len([f for f in os.listdir(solutions_folder) if f.endswith('_w.txt')])
        
        print(f"📊 Generated files:")
        print(f"   Constraint files: {constraint_files}")
        print(f"   Solution files: {solution_files}")
        
        # Read CSV summary
        try:
            import pandas as pd
            df = pd.read_csv(csv_file)
            feasible_count = df['feasible'].sum()
            avg_makespan = df[df['feasible']]['makespan'].mean()
            avg_comp_time = df['computational_time'].mean()
            
            print(f"📊 Summary statistics:")
            print(f"   Total instances: {len(df)}")
            print(f"   Feasible instances: {feasible_count}")
            print(f"   Success rate: {feasible_count/len(df)*100:.1f}%")
            print(f"   Average makespan: {avg_makespan:.2f}")
            print(f"   Average computation time: {avg_comp_time:.3f}s")
            
        except ImportError:
            print("   (Install pandas for detailed statistics)")
        except Exception as e:
            print(f"   Error reading CSV: {e}")
    else:
        print(f"❌ Results file not found: {csv_file}")
        return False
    
    print(f"\n⏱️  Total execution time: {total_time/60:.1f} minutes")
    print("\n🎉 Generation process completed!")
    
    # Provide next steps
    print("\n📋 Next steps:")
    print("1. Review the generated CSV file for detailed results")
    print("2. Use lr_scheduler_train.py to train on the generated data:")
    print("   python3 lr_scheduler_train.py --num-robots 5 --train-end-no 1000")
    print("3. The data is ready for 5-robot 30-task scheduling experiments")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
