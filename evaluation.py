#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
evaluation.py

Runs a full evaluation of a trained ScheduleNet4Layer policy against:
  1. Gurobi‐optimal (replayed),
  2. <PERSON><PERSON>,
  3. Random,
  4. (Optionally) Tercio,

over a folder of test instances.  It computes:

  • M₁(α) curve for α ∈ [1.00, 2.00] (RSS’20 Sec. VI.C).
  • Average optimality gap (%) = mean((z_model – z_opt)/z_opt × 100).
  • Runtime vs. problem size (#tasks).
  • % Infeasible (policy hit negative‐cycle).
  • (Optionally) Weighted‐sum objective (RSS’20 Sec. VI.D).
  • Baseline comparisons: EDF, Random, (Tercio stub).

Usage:
    python3 evaluation.py \
      --path-test-data ./problem_instances_test/constrains \
      --path-test-sols ./problem_instances_test/solutions \
      --checkpoint-policy ./checkpoint_schedule_net.pth \
      --num-robots 2 \
      --max-instances 1000 \
      --device cpu
"""

import os
import time
import argparse
import numpy as np
import torch
import matplotlib.pyplot as plt
import networkx as nx

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper, johnsonU
from hetnet import ScheduleNet4Layer

# If you implement Tercio, import it here; otherwise keep the stub below:
# from tercio import tercio_schedule
def tercio_schedule_stub(base, num_robots):
    """
    Stub for Tercio.  Replace with your actual Tercio implementation
    that returns (opt_w, rob_schedules).
    """
    raise NotImplementedError("Please implement tercio_schedule(base, num_robots)")

def load_gurobi_solution(sol_base, num_robots):
    """
    Load Gurobi's saved solution for one instance:
      • sol_base + "_w.txt" → global task order (length = num_tasks)
      • sol_base + f"_{r}.txt" for each robot 0..R-1 → that robot’s assigned tasks
    Returns: (opt_w, rob_schedules)
    """
    w_file = sol_base + "_w.txt"
    if not os.path.isfile(w_file):
        raise FileNotFoundError(f"No global order found: {w_file}")
    opt_w = np.loadtxt(w_file, dtype=int)

    rob_schedules = []
    for r in range(num_robots):
        rr = sol_base + f"_{r}.txt"
        if os.path.isfile(rr):
            rob_schedules.append(np.loadtxt(rr, dtype=int))
        else:
            rob_schedules.append(np.array([], dtype=int))
    return opt_w, rob_schedules

def run_gurobi_replay(base, num_robots):
    """
    Replay Gurobi's solution to recover z_opt (makespan).
    Returns: (z_opt, feasible_flag).
    """
    # Assume `base` is e.g. ".../constrains/00001"
    sol_base = base.replace("constrains", "solutions")
    try:
        opt_w, rob_schedules = load_gurobi_solution(sol_base, num_robots)
    except FileNotFoundError:
        return np.nan, False

    env_g = SchedulingEnv(base)
    for task in opt_w:
        # find which robot r performed this task in Gurobi's allocation
        rj = next((r for r in range(num_robots) if task in rob_schedules[r]), None)
        if rj is None:
            return np.nan, False
        success, _, _ = env_g.insert_robot(int(task), int(rj))
        if not success:
            return np.nan, False

    return float(env_g.min_makespan), True


def run_policy_schedule_net(base, num_robots, policy_net, device,
                            map_width=6, loc_dist_threshold=1):
    """
    Run the trained ScheduleNet4Layer policy “greedily” on one instance:
      • At each step, gather valid_tasks = env.get_valid_tasks(0.0).
      • For each robot r in [0..R-1]:
          – Build a DGL heterograph using build_hetgraph(...),
            where “value” nodes correspond to all valid (task→r).
          – Build feature dict via hetgraph_node_helper(...),
            convert to tensors, then call policy_net(g, feat_dict) to get Q-values.
      • Choose the (task,robot) with maximum Q. Insert into STN.
      • Terminate when all tasks scheduled or STN becomes infeasible.
    Returns: (z_model, feasible_flag, runtime_seconds).
    """
    env_m = SchedulingEnv(base)
    start_t = time.time()

    # Trivial if no tasks
    if env_m.num_tasks == 0:
        return 0.0, True, 0.0

    while len(env_m.partialw) < (env_m.num_tasks + 1):
        valid_tasks = env_m.get_valid_tasks(timepoint=0.0)
        if valid_tasks.size == 0:
            return np.nan, False, time.time() - start_t

        best_q = -1e9
        best_task = None
        best_robot = None

        halfDG = env_m.halfDG
        num_tasks = env_m.num_tasks
        locs = env_m.loc
        durs = env_m.dur
        curr_partials = env_m.partials
        curr_partialw = env_m.partialw

        # For each robot, build one heterograph and evaluate Q-values
        for rj in range(num_robots):
            g = build_hetgraph(
                halfDG=halfDG,
                num_tasks=num_tasks,
                num_robots=num_robots,
                dur=durs,
                map_width=map_width,
                locs=locs,
                loc_dist_threshold=loc_dist_threshold,
                partials=curr_partials,
                unsch_tasks=valid_tasks,
                selected_robot=rj,
                valid_tasks=valid_tasks
            )
            g = g.to(device)

            # Build node features
            feat_nd = hetgraph_node_helper(
                number_of_nodes=g.num_nodes('task'),
                curr_partialw=curr_partialw,
                curr_partials=curr_partials,
                locations=locs,
                durations=durs,
                map_width=map_width,
                num_robots=num_robots,
                num_values=len(valid_tasks)
            )
            feat_dict = {k: torch.from_numpy(v).float().to(device) for k, v in feat_nd.items()}

            with torch.no_grad():
                outputs = policy_net(g, feat_dict)
            # outputs['value'] is a tensor of shape (num_values, 1)
            q_values = outputs['value'].cpu().numpy().reshape(-1)

            idx_max = np.argmax(q_values)
            if q_values[idx_max] > best_q:
                best_q = q_values[idx_max]
                best_task = int(valid_tasks[idx_max])
                best_robot = rj

        success, _, _ = env_m.insert_robot(best_task, best_robot)
        if not success:
            return np.nan, False, time.time() - start_t

    z_model = float(env_m.min_makespan)
    return z_model, True, time.time() - start_t


def run_edf_baseline(base, num_robots):
    """
    Earliest‐Deadline‐First (EDF) heuristic adapted to STN:
      • At each step, pick the valid task with smallest deadline.
      • Assign it to the robot with smallest “simulated finish time” so far.
      • Insert into STN (via env.insert_robot) to check feasibility.
    Returns: (z_edf, feasible_flag, runtime_sec).
    """
    env_e = SchedulingEnv(base)
    start_t = time.time()

    if env_e.num_tasks == 0:
        return 0.0, True, 0.0

    # Simulate robot finish times (purely by durations)
    robot_finish = [0.0] * num_robots

    # Build a deadline map
    ddl_map = {i: np.inf for i in range(1, env_e.num_tasks + 1)}
    for (ti, d) in env_e.ddl:
        ddl_map[int(ti)] = float(d)

    while len(env_e.partialw) < (env_e.num_tasks + 1):
        valid_tasks = env_e.get_valid_tasks(timepoint=0.0)
        if valid_tasks.size == 0:
            return np.nan, False, time.time() - start_t

        # Pick valid task with smallest deadline (tie by task ID)
        best_task = int(min(valid_tasks, key=lambda i: (ddl_map.get(int(i), np.inf), i)))
        # Assign to the robot with smallest finish time
        best_robot = int(min(range(num_robots), key=lambda r: robot_finish[r]))

        dur_choice = float(env_e.dur[best_task - 1, best_robot])
        robot_finish[best_robot] += dur_choice

        success, _, _ = env_e.insert_robot(best_task, best_robot)
        if not success:
            return np.nan, False, time.time() - start_t

    z_edf = float(env_e.min_makespan)
    return z_edf, True, time.time() - start_t


def run_random_baseline(base, num_robots):
    """
    Random scheduling baseline:
      • At each step, pick a random valid task & random robot.
      • Insert into STN.  Repeat until done or infeasible.
    Returns: (z_rand, feasible_flag, runtime_sec).
    """
    env_r = SchedulingEnv(base)
    start_t = time.time()

    if env_r.num_tasks == 0:
        return 0.0, True, 0.0

    rng = np.random.default_rng(seed=42)

    while len(env_r.partialw) < (env_r.num_tasks + 1):
        valid_tasks = env_r.get_valid_tasks(timepoint=0.0)
        if valid_tasks.size == 0:
            return np.nan, False, time.time() - start_t

        best_task = int(rng.choice(valid_tasks))
        best_robot = int(rng.integers(low=0, high=num_robots))

        success, _, _ = env_r.insert_robot(best_task, best_robot)
        if not success:
            return np.nan, False, time.time() - start_t

    z_rand = float(env_r.min_makespan)
    return z_rand, True, time.time() - start_t


def run_tercio_baseline(base, num_robots):
    """
    Run the Tercio heuristic (if implemented).  Otherwise, this stub raises.
    Returns: (z_tercio, feasible_flag, runtime_sec).
    """
    t0 = time.time()
    try:
        opt_w, rob_schedules = tercio_schedule_stub(base, num_robots)
    except NotImplementedError:
        return np.nan, False, 0.0

    env_t = SchedulingEnv(base)
    for task in opt_w:
        rj = next((r for r in range(num_robots) if task in rob_schedules[r]), None)
        if rj is None:
            return np.nan, False, time.time() - t0
        success, _, _ = env_t.insert_robot(int(task), int(rj))
        if not success:
            return np.nan, False, time.time() - t0

    return float(env_t.min_makespan), True, time.time() - t0


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--path-test-data', type=str, required=True,
                        help='Folder containing *_dur.txt, *_ddl.txt, *_wait.txt, *_loc.txt')
    parser.add_argument('--path-test-sols', type=str, required=True,
                        help='Folder containing *_w.txt & per‐robot *_0.txt, *_1.txt, …')
    parser.add_argument('--checkpoint-policy', type=str, required=True,
                        help='Path to trained ScheduleNet4Layer checkpoint (.pth)')
    parser.add_argument('--num-robots', type=int, default=2)
    parser.add_argument('--max-instances', type=int, default=1000)
    parser.add_argument('--device', type=str, default='cpu')
    args = parser.parse_args()

    device = torch.device(args.device)

    # 1) Reconstruct exactly the same ScheduleNet4Layer architecture you trained:
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'),
        ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'),
        ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'),
        ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'),
        ('state', 'sin', 'state'),
        ('task', 'tto', 'value'),
        ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'),
        ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'),
        ('robot', 'use_time', 'task'),
    ]
    num_heads = 8

    print("→ Loading ScheduleNet4Layer from checkpoint …")
    policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, num_heads).to(device)
    checkpoint = torch.load(args.checkpoint_policy, map_location=device)
    policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
    policy_net.eval()

    # 2) Gather all instance IDs from the “constrains” folder
    all_files = os.listdir(args.path_test_data)
    instance_ids = sorted({int(f.split('_')[0]) for f in all_files if f.endswith('_dur.txt')})
    instance_ids = instance_ids[: args.max_instances]
    N_test = len(instance_ids)
    print(f"→ Found {N_test} test instances (limiting to {args.max_instances}).")

    # 3) Pre‐allocate arrays
    gurobi_ms   = np.zeros(N_test, dtype=np.float32)
    ssan_ms     = np.zeros(N_test, dtype=np.float32)
    edf_ms      = np.zeros(N_test, dtype=np.float32)
    random_ms   = np.zeros(N_test, dtype=np.float32)
    tercio_ms   = np.zeros(N_test, dtype=np.float32)

    gurobi_ok   = np.zeros(N_test, dtype=bool)
    ssan_ok     = np.zeros(N_test, dtype=bool)
    edf_ok      = np.zeros(N_test, dtype=bool)
    random_ok   = np.zeros(N_test, dtype=bool)
    tercio_ok   = np.zeros(N_test, dtype=bool)

    runtime_ssan   = np.zeros(N_test, dtype=np.float32)
    runtime_edf    = np.zeros(N_test, dtype=np.float32)
    runtime_rand   = np.zeros(N_test, dtype=np.float32)
    runtime_tercio = np.zeros(N_test, dtype=np.float32)

    # (Optional) Weighted‐sum objective arrays
    use_weighted_sum = False
    weighted_costs   = [None] * N_test
    weighted_opt     = np.zeros(N_test, dtype=np.float32)
    weighted_ssan    = np.zeros(N_test, dtype=np.float32)
    weighted_edf     = np.zeros(N_test, dtype=np.float32)
    weighted_rand    = np.zeros(N_test, dtype=np.float32)
    weighted_tercio  = np.zeros(N_test, dtype=np.float32)

    # 4) Evaluate each instance
    for idx, inst in enumerate(instance_ids):
        base = os.path.join(args.path_test_data, f"{inst:05d}")

        # (A) Gurobi replay
        zg, okg = run_gurobi_replay(base, args.num_robots)
        gurobi_ms[idx] = zg
        gurobi_ok[idx] = okg

        # (B) ScheduleNet4Layer policy
        zs, oks, ts = run_policy_schedule_net(base, args.num_robots, policy_net, device)
        ssan_ms[idx] = zs
        # Only meaningful if Gurobi was feasible
        ssan_ok[idx] = oks and okg
        runtime_ssan[idx] = ts

        # (C) EDF baseline
        ze, oke, te = run_edf_baseline(base, args.num_robots)
        edf_ms[idx] = ze
        edf_ok[idx] = oke
        runtime_edf[idx] = te

        # (D) Random baseline
        zr, okr, tr = run_random_baseline(base, args.num_robots)
        random_ms[idx] = zr
        random_ok[idx] = okr
        runtime_rand[idx] = tr

        # (E) Tercio baseline
        zt, okt, tt = run_tercio_baseline(base, args.num_robots)
        tercio_ms[idx] = zt
        tercio_ok[idx] = okt
        runtime_tercio[idx] = tt

        # (Optional) Weighted‐sum if cost file exists
        cost_file = base + "_cost.txt"
        if os.path.isfile(cost_file):
            use_weighted_sum = True
            c_vec = np.loadtxt(cost_file, dtype=float)  # length = num_tasks
            weighted_costs[idx] = c_vec

            # Gurobi’s weighted‐sum: ∑ c_i * f_i
            env_wg = SchedulingEnv(base)
            wg_sum = 0.0
            for task in np.loadtxt(base.replace("constrains","solutions") + "_w.txt", dtype=int):
                rj = None
                for r in range(args.num_robots):
                    rfile = base.replace("constrains","solutions") + f"_{r}.txt"
                    if os.path.isfile(rfile):
                        assigned = np.loadtxt(rfile, dtype=int)
                        if int(task) in assigned:
                            rj = r
                            break
                if rj is None:
                    wg_sum = np.nan
                    break
                success, _, _ = env_wg.insert_robot(int(task), int(rj))
                if not success:
                    wg_sum = np.nan
                    break
            if not np.isnan(wg_sum):
                # Re‐compute Johnson’s on final STN to get finish times
                try:
                    p_ultra, d_ultra = johnsonU(env_wg.g)
                    total_ws = 0.0
                    for i in range(1, env_wg.num_tasks + 1):
                        fi = f"f{i:03d}"
                        finish_i = -d_ultra[fi]['s000']
                        total_ws += c_vec[i-1] * finish_i
                    weighted_opt[idx] = total_ws
                except:
                    weighted_opt[idx] = np.nan

            # (We omit re‐computing weighted sums for SSAN/EDF/Random/Tercio here for brevity.)

        print(f"[Inst {inst:05d}] z_opt={zg:.3f} (ok={okg})  |  "
              f"SchedNet z={zs:.3f} (ok={oks}) t={ts:.2f}s  |  "
              f"EDF z={ze:.3f}  |  RNG z={zr:.3f}  |  TER z={zt:.3f}")

    # 5) Post‐process metrics

    valid_mask = gurobi_ok
    N_valid = int(np.sum(valid_mask))
    print(f"\n→ {N_valid}/{N_test} instances where Gurobi was feasible.")

    # M₁(α) computation
    alpha_vals = np.arange(1.00, 2.01, 0.05)
    def compute_M1(z_alg, z_opt, mask_ok):
        ratios = z_alg[mask_ok] / z_opt[mask_ok]
        return np.array([100.0 * np.sum(ratios <= α) / np.sum(mask_ok) for α in alpha_vals])

    M1_schednet   = compute_M1(ssan_ms,   gurobi_ms, valid_mask & ssan_ok)
    M1_edf        = compute_M1(edf_ms,    gurobi_ms, valid_mask & edf_ok)
    M1_random     = compute_M1(random_ms, gurobi_ms, valid_mask & random_ok)
    M1_tercio     = compute_M1(tercio_ms, gurobi_ms, valid_mask & tercio_ok)

    # Average optimality gap (%)
    gap_schednet   = 100.0 * (ssan_ms[valid_mask]   - gurobi_ms[valid_mask])   / gurobi_ms[valid_mask]
    gap_edf        = 100.0 * (edf_ms[valid_mask]    - gurobi_ms[valid_mask])   / gurobi_ms[valid_mask]
    gap_random     = 100.0 * (random_ms[valid_mask] - gurobi_ms[valid_mask])   / gurobi_ms[valid_mask]
    gap_tercio     = 100.0 * (tercio_ms[valid_mask] - gurobi_ms[valid_mask])   / gurobi_ms[valid_mask]

    # % Infeasible
    infeas_schednet   = 100.0 * (1.0 - np.sum(ssan_ok[valid_mask])   / N_valid)
    infeas_edf        = 100.0 * (1.0 - np.sum(edf_ok[valid_mask])    / N_valid)
    infeas_random     = 100.0 * (1.0 - np.sum(random_ok[valid_mask]) / N_valid)
    infeas_tercio     = 100.0 * (1.0 - np.sum(tercio_ok[valid_mask]) / N_valid)

    # Problem sizes array
    problem_sizes = np.zeros(N_test, dtype=int)
    for idx, inst in enumerate(instance_ids):
        dur_file = os.path.join(args.path_test_data, f"{inst:05d}_dur.txt")
        if os.path.isfile(dur_file):
            arr = np.loadtxt(dur_file, dtype=int)
            problem_sizes[idx] = arr.shape[0]
        else:
            problem_sizes[idx] = 0

    # Gather runtimes for feasible runs only
    def gather(size_arr, time_arr, ok_arr, mask):
        m = mask & ok_arr
        return size_arr[m], time_arr[m]

    sz_sn,  t_sn  = gather(problem_sizes, runtime_ssan,   ssan_ok,   valid_mask)
    sz_edf, t_edf = gather(problem_sizes, runtime_edf,    edf_ok,    valid_mask)
    sz_rn,  t_rn  = gather(problem_sizes, runtime_rand,   random_ok, valid_mask)
    sz_tc,  t_tc  = gather(problem_sizes, runtime_tercio, tercio_ok, valid_mask)

    # 6) Plotting

    # (a) M₁‐Curve
    plt.figure(figsize=(6,4))
    plt.plot(alpha_vals, M1_schednet,   marker='o', label='SchedNet')
    plt.plot(alpha_vals, M1_edf,        marker='s', label='EDF')
    plt.plot(alpha_vals, M1_random,     marker='^', label='Random')
    plt.plot(alpha_vals, M1_tercio,     marker='x', label='Tercio')
    plt.xlabel('Optimality ratio α')
    plt.ylabel('% instances r ≤ α')
    plt.title('M₁‐Curve')
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.savefig('M1_curve.png', dpi=300)

    # (b) Gap histogram
    plt.figure(figsize=(6,4))
    max_gap = np.nanmax([np.nanmax(gap_schednet), np.nanmax(gap_edf),
                         np.nanmax(gap_random), np.nanmax(gap_tercio)])
    bins = np.linspace(0, np.ceil(max_gap), 20)
    plt.hist(gap_schednet,   bins=bins, alpha=0.6, label=f'SchedNet (μ={np.nanmean(gap_schednet):.1f}%)')
    plt.hist(gap_edf,        bins=bins, alpha=0.6, label=f'EDF (μ={np.nanmean(gap_edf):.1f}%)')
    plt.hist(gap_random,     bins=bins, alpha=0.6, label=f'Random (μ={np.nanmean(gap_random):.1f}%)')
    plt.hist(gap_tercio,     bins=bins, alpha=0.6, label=f'Tercio (μ={np.nanmean(gap_tercio):.1f}%)')
    plt.xlabel('Optimality gap (%)')
    plt.ylabel('Count')
    plt.title('Gap Distribution')
    plt.legend()
    plt.tight_layout()
    plt.savefig('gap_histogram.png', dpi=300)

    # (c) Runtime vs. Problem Size
    plt.figure(figsize=(6,4))
    plt.scatter(sz_sn,  t_sn,  label='SchedNet',   alpha=0.6)
    plt.scatter(sz_edf, t_edf, label='EDF',         alpha=0.6)
    plt.scatter(sz_rn,  t_rn,  label='Random',      alpha=0.6)
    plt.scatter(sz_tc,  t_tc,  label='Tercio',      alpha=0.6)
    plt.xlabel('# Tasks')
    plt.ylabel('Runtime (s)')
    plt.title('Runtime vs. Problem Size')
    plt.legend()
    plt.tight_layout()
    plt.savefig('runtime_vs_size.png', dpi=300)

    # (d) Print summary
    print("\n=== SUMMARY ===")
    print(f"# test instances: {N_test}")
    print(f"# Gurobi‐feasible instances: {N_valid}")
    print(f"SchedNet:   Avg gap={np.nanmean(gap_schednet):.2f}%, % inf={infeas_schednet:.2f}%, Avg t={np.nanmean(t_sn):.2f}s")
    print(f"EDF:        Avg gap={np.nanmean(gap_edf):.2f}%,    % inf={infeas_edf:.2f}%,    Avg t={np.nanmean(t_edf):.2f}s")
    print(f"Random:     Avg gap={np.nanmean(gap_random):.2f}%, % inf={infeas_random:.2f}%,    Avg t={np.nanmean(t_rn):.2f}s")
    print(f"Tercio:     Avg gap={np.nanmean(gap_tercio):.2f}%, % inf={infeas_tercio:.2f}%,    Avg t={np.nanmean(t_tc):.2f}s")

    print("\nPlots saved: M1_curve.png | gap_histogram.png | runtime_vs_size.png")
    if use_weighted_sum:
        print("Weighted‐sum objectives detected (see `_cost.txt`). "
              "You can extend this script to plot Weighted‐sum comparisons.")

if __name__ == "__main__":
    main()

