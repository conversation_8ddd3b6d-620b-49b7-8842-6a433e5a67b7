# -*- coding: utf-8 -*-
"""
Created on Sun Sep 20 14:58:41 2020

@author: pheno

Generates feasible STN scheduling instances and saves their makespans to a CSV.
"""

import os
import csv
import numpy as np
import gurobipy as gp
from gurobipy import GRB
from datetime import datetime

from utils import SchedulingEnv
from benchmark.JohnsonUltra import johnsonU

# Configuration
NUM_INSTANCES = 5
NUM_TASKS = 30
NUM_ROBOTS = 2
OUTPUT_FOLDER = "problem_instances_train_30"
CONSTRAINTS_FOLDER = os.path.join(OUTPUT_FOLDER, "constraints")
SOLUTIONS_FOLDER = os.path.join(OUTPUT_FOLDER, "solutions")
MAKESPAN_CSV = os.path.join(OUTPUT_FOLDER, "makespans.csv")
T = 5  # Time multiplier for deadlines

os.makedirs(CONSTRAINTS_FOLDER, exist_ok=True)
os.makedirs(SOLUTIONS_FOLDER, exist_ok=True)


def detect_negative_cycle(G):
    """Return True if <PERSON>'s algorithm detects a negative cycle."""
    try:
        _, _ = johnsonU(G)
        return False
    except Exception:
        return True


def solve_makespan(env):
    """
    Use Gurobi to minimize makespan. Returns:
      - optimal makespan (float) if feasible
      - assignments dict {robot: [task_ids]}, otherwise (None, None).
    """
    model = gp.Model("makespan")
    model.setParam("OutputFlag", 0)

    tasks = range(1, NUM_TASKS + 1)
    robots = range(NUM_ROBOTS)

    # Decision variables
    start_times = model.addVars(tasks, lb=0, vtype=GRB.CONTINUOUS, name="start")
    x = model.addVars(tasks, robots, vtype=GRB.BINARY, name="assign")

    makespan = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="makespan")
    model.setObjective(makespan, GRB.MINIMIZE)

    # Each task must be assigned exactly one robot
    for t in tasks:
        model.addConstr(gp.quicksum(x[t, r] for r in robots) == 1, f"assign_{t}")

    # Duration constraints: start_t + duration(t,r) ≤ makespan
    for t in tasks:
        model.addConstr(
            start_times[t]
            + gp.quicksum(x[t, r] * env.dur[t - 1, r] for r in robots)
            <= makespan,
            f"dur_{t}"
        )

    # Deadline constraints
    for task_id, deadline in env.ddl:
        model.addConstr(start_times[task_id] <= deadline, f"deadline_{task_id}")

    # Waiting constraints: start_ti + wait_time ≤ start_tj
    for ti, tj, wait_time in env.wait:
        model.addConstr(start_times[ti] + wait_time <= start_times[tj], f"wait_{ti}_{tj}")

    model.optimize()

    if model.status == GRB.OPTIMAL:
        assignments = {r: [] for r in robots}
        for t in tasks:
            for r in robots:
                if x[t, r].X > 0.5:
                    assignments[r].append(t)
                    break
        # Sort each robot's tasks by start time for consistent insertion order
        for r in robots:
            assignments[r].sort(key=lambda task: start_times[task].X)
        return model.objVal, assignments
    else:
        return None, None


def partial_schedule_feasibility_check(env, assignments):
    """
    Simulate incremental insertion into the STN using the Gurobi assignment order.
    Return False if a negative cycle arises at any step.
    """
    env.partials = [np.zeros(1, dtype=np.int32) for _ in range(env.num_robots)]
    env.partialw = np.zeros(1, dtype=np.int32)

    for r in range(env.num_robots):
        for task in assignments[r]:
            success, _, _ = env.insert_robot(task, r)
            if not success:
                return False
    return True


def generate_feasible_problem(instance_id):
    """
    Attempt up to max_attempts to generate a single feasible instance.
    On success, saves constraints and solutions files, returns makespan.
    On failure, returns None.
    """
    max_attempts = 100
    attempt = 0

    while attempt < max_attempts:
        attempt += 1

        # 1) Generate random duration matrix [1,10]
        durations = np.random.randint(1, 11, size=(NUM_TASKS, NUM_ROBOTS))

        # 2) Initialize a dummy SchedulingEnv without loading from file
        env = SchedulingEnv.__new__(SchedulingEnv)
        env.dur = durations
        env.ddl = np.empty((0, 2), dtype=int)
        env.wait = np.empty((0, 3), dtype=int)
        env.loc = np.column_stack((
            np.random.randint(1, NUM_ROBOTS + 1, size=NUM_TASKS),
            np.random.randint(1, NUM_ROBOTS + 1, size=NUM_TASKS)
        ))
        env.num_tasks = NUM_TASKS
        env.num_robots = NUM_ROBOTS
        env.M = NUM_TASKS * 10.0
        env.C = 3.0
        env.max_deadline = NUM_TASKS * 10
        env.partials = [np.zeros(1, dtype=np.int32) for _ in range(NUM_ROBOTS)]
        env.partialw = np.zeros(1, dtype=np.int32)

        # Build initial STN
        env.g = env.initialize_STN()

        # Initialize min_makespan to avoid attribute errors
        success, min_makespan = env.check_consistency_makespan()
        if not success:
            print(f"Initial STN infeasible for instance {instance_id}, attempt {attempt}")
            continue
        env.min_makespan = min_makespan

        # 3) Add deadlines incrementally
        num_deadlines = max(1, int(0.25 * NUM_TASKS))
        deadlines = []
        discarded_deadlines = 0
        deadline_attempts = 0

        while len(deadlines) < num_deadlines and deadline_attempts < 200:
            deadline_attempts += 1
            task_id = np.random.randint(1, NUM_TASKS + 1)
            max_time = NUM_TASKS * T - durations[task_id - 1].max()
            if max_time <= 5:
                continue
            deadline = np.random.randint(5, max_time)
            fi = f'f{task_id:03d}'
            env.g.add_edge('s000', fi, weight=deadline)
            if detect_negative_cycle(env.g):
                env.g.remove_edge('s000', fi)
                discarded_deadlines += 1
            else:
                deadlines.append([task_id, deadline])
        deadlines = np.array(deadlines)

        # 4) Add waiting constraints incrementally
        num_wait_constraints = max(1, int(0.25 * NUM_TASKS))
        wait_constraints = []
        discarded_waits = 0
        wait_attempts = 0

        while len(wait_constraints) < num_wait_constraints and wait_attempts < 400:
            wait_attempts += 1
            i, j = np.random.choice(range(1, NUM_TASKS + 1), size=2, replace=False)
            if i >= j:
                continue
            wait_time = np.random.randint(1, 11)
            si = f's{i:03d}'
            fj = f'f{j:03d}'
            env.g.add_edge(si, fj, weight=-wait_time)
            if detect_negative_cycle(env.g):
                env.g.remove_edge(si, fj)
                discarded_waits += 1
            else:
                wait_constraints.append([i, j, wait_time])
        wait_constraints = np.array(wait_constraints)

        if detect_negative_cycle(env.g):
            print(f"Negative cycle detected after constraints, instance {instance_id}, attempt {attempt}")
            continue

        # 5) Solve makespan via Gurobi
        env.ddl = deadlines
        env.wait = wait_constraints
        optimal_makespan, gurobi_assignments = solve_makespan(env)
        if optimal_makespan is None or gurobi_assignments is None:
            print(f"Gurobi failed on instance {instance_id}, attempt {attempt}")
            continue

        # 6) Check partial‐schedule feasibility using Gurobi assignments
        if not partial_schedule_feasibility_check(env, gurobi_assignments):
            print(f"Partial schedule check failed, instance {instance_id}, attempt {attempt}")
            continue

        # 7) Save constraints to disk
        prefix_c = os.path.join(CONSTRAINTS_FOLDER, f"{instance_id:05d}")
        np.savetxt(f"{prefix_c}_dur.txt", durations, fmt="%d")
        np.savetxt(f"{prefix_c}_ddl.txt", deadlines, fmt="%d")
        np.savetxt(f"{prefix_c}_wait.txt", wait_constraints, fmt="%d")
        np.savetxt(f"{prefix_c}_loc.txt", env.loc, fmt="%d")

        # 8) Save solutions to disk
        prefix_s = os.path.join(SOLUTIONS_FOLDER, f"{instance_id:05d}")

        # 8a) Global task order (_w.txt): concatenated by robot assignment
        task_order = []
        for r in range(NUM_ROBOTS):
            task_order.extend(gurobi_assignments[r])
        task_order = np.array(task_order).reshape(-1, 1)
        np.savetxt(f"{prefix_s}_w.txt", task_order, fmt="%d")

        # 8b) Individual robot schedules (_0.txt, _1.txt, …)
        for r in range(NUM_ROBOTS):
            robot_tasks = np.array(gurobi_assignments[r]).reshape(-1, 1) \
                if gurobi_assignments[r] else np.empty((0, 1), dtype=int)
            np.savetxt(f"{prefix_s}_{r}.txt", robot_tasks, fmt="%d")

        print(f"✅ Instance {instance_id} generated | makespan={optimal_makespan:.2f} | "
              f"Attempts={attempt} | discarded_deadlines={discarded_deadlines} | discarded_waits={discarded_waits}")

        return optimal_makespan

    print(f"❌ Failed to generate instance {instance_id} after {max_attempts} attempts.")
    return None


if __name__ == "__main__":
    start_time = datetime.now()
    print(f"🚀 Generating {NUM_INSTANCES} problem instances…\n")

    makespan_records = []  # will hold tuples (instance_id, makespan)
    success_count = 0

    for instance_id in range(1, NUM_INSTANCES + 1):
        mspan = generate_feasible_problem(instance_id)
        if mspan is not None:
            success_count += 1
            makespan_records.append((instance_id, mspan))

    end_time = datetime.now()
    print(f"\n✅ Successfully generated {success_count}/{NUM_INSTANCES} instances.")
    print(f"⏳ Total time: {end_time - start_time}")

    # Save makespan records to CSV
    with open(MAKESPAN_CSV, mode='w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["instance_id", "makespan"])
        for inst_id, mspan in makespan_records:
            writer.writerow([inst_id, mspan])

    print(f"💾 Makespans saved to {MAKESPAN_CSV}")

