import os
import numpy as np
import networkx as nx
import gurobipy as gp
from gurobipy import GRB
import shutil

# Configurations
NUM_INSTANCES = 10  # Number of new instances to generate
NUM_TASKS = 17
NUM_ROBOTS = 5
INPUT_FOLDER = "./gen"
OUTPUT_FOLDER = "./generated_instances"
T = 2  # Time multiplier for five-robot teams

# Ensure output directory exists
if os.path.exists(OUTPUT_FOLDER):
    shutil.rmtree(OUTPUT_FOLDER)  # Clear existing instances
os.makedirs(OUTPUT_FOLDER, exist_ok=True)


def load_original_instance():
    """Load the original constraints from the provided files."""
    ddl = np.loadtxt(f"{INPUT_FOLDER}/00001_ddl.txt", dtype=int)
    dur = np.loadtxt(f"{INPUT_FOLDER}/00001_dur.txt", dtype=int)
    loc = np.loadtxt(f"{INPUT_FOLDER}/00001_loc.txt", dtype=int)
    wait = np.loadtxt(f"{INPUT_FOLDER}/00001_wait.txt", dtype=int)
    return ddl, dur, loc, wait


def modify_constraints(ddl, dur, loc, wait, instance_id):
    """Modify the constraints slightly to prevent negative cycles."""
    
    # 1️⃣ **Increase Deadlines Slightly**
    new_ddl = ddl.copy()
    new_ddl[:, 1] += np.random.randint(1, 3, size=new_ddl.shape[0])  # +1 to +2 buffer

    # 2️⃣ **Slightly Modify Durations**
    new_dur = dur.copy()
    for i in range(NUM_TASKS):
        j = np.random.randint(0, NUM_ROBOTS)  # Pick a random robot
        new_dur[i, j] += np.random.choice([-1, 0, 1])  # Small adjustment

    # 3️⃣ **Increase Wait Constraints Slightly**
    new_wait = wait.copy()
    new_wait[:, 2] += np.random.randint(1, 3, size=new_wait.shape[0])  # Increase wait by 1-2

    return new_ddl, new_dur, loc, new_wait


def solve_makespan(dur, ddl, wait):
    """Use Gurobi to find the optimal makespan and task order."""
    model = gp.Model("makespan")
    model.setParam("OutputFlag", 0)

    # Decision variables: Start times for tasks
    start_times = model.addVars(range(1, NUM_TASKS + 1), lb=0, vtype=GRB.CONTINUOUS, name="start")

    # Makespan variable (time to complete all tasks)
    makespan = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="makespan")

    # Objective: Minimize makespan
    model.setObjective(makespan, GRB.MINIMIZE)

    # Constraints: Task dependencies (durations, deadlines, waiting constraints)
    for i in range(NUM_TASKS):
        dur_min = dur[i].min()
        model.addConstr(start_times[i + 1] + dur_min <= makespan, f"task_{i}_dur")

    for task_id, deadline in ddl:
        model.addConstr(start_times[task_id] <= deadline, f"deadline_{task_id}")

    for ti, tj, wait_time in wait:
        model.addConstr(start_times[ti] + wait_time <= start_times[tj], f"wait_{ti}_{tj}")

    # Solve model
    model.optimize()

    if model.status == GRB.OPTIMAL:
        # Extract optimal task execution order
        task_order = sorted(start_times.keys(), key=lambda x: start_times[x].X)
        return task_order, model.objVal
    else:
        return None, None  # No feasible solution


def save_instance(instance_id, ddl, dur, loc, wait, task_order):
    """Save the generated instance and its solution."""
    np.savetxt(f"{OUTPUT_FOLDER}/{instance_id:05d}_ddl.txt", ddl, fmt="%d")
    np.savetxt(f"{OUTPUT_FOLDER}/{instance_id:05d}_dur.txt", dur, fmt="%d")
    np.savetxt(f"{OUTPUT_FOLDER}/{instance_id:05d}_loc.txt", loc, fmt="%d")
    np.savetxt(f"{OUTPUT_FOLDER}/{instance_id:05d}_wait.txt", wait, fmt="%d")
    np.savetxt(f"{OUTPUT_FOLDER}/{instance_id:05d}_w.txt", np.array(task_order).reshape(-1, 1), fmt="%d")


def generate_problem_instances():
    """Generate 10 modified problem instances."""
    original_ddl, original_dur, original_loc, original_wait = load_original_instance()

    success_count = 0
    for instance_id in range(2, NUM_INSTANCES + 2):  # Start from 00002
        attempt = 0
        while attempt < 10:  # Retry if a problem instance is infeasible
            attempt += 1

            # 1️⃣ **Modify constraints slightly**
            new_ddl, new_dur, new_loc, new_wait = modify_constraints(original_ddl, original_dur, original_loc, original_wait, instance_id)

            # 2️⃣ **Solve for optimal schedule using Gurobi**
            task_order, makespan = solve_makespan(new_dur, new_ddl, new_wait)
            if task_order is not None:
                # 3️⃣ **Save successful instance**
                save_instance(instance_id, new_ddl, new_dur, new_loc, new_wait, task_order)
                print(f"✅ Instance {instance_id:05d} generated (Makespan: {makespan})")
                success_count += 1
                break
            else:
                print(f"⚠️ Instance {instance_id:05d} infeasible, retrying... (Attempt {attempt})")

    print(f"🎯 Successfully generated {success_count} problem instances!")


if __name__ == "__main__":
    generate_problem_instances()

