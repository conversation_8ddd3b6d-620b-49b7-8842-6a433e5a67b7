# test_all_instances.py
# -*- coding: utf-8 -*-
"""
Batch‐test all SchedulingEnv instances under a folder.

Directory structure must be:

    problem_instances_test/
        constrains/
            00001_dur.txt
            00001_ddl.txt
            00001_wait.txt
            00001_loc.txt
            00002_dur.txt
            00002_ddl.txt
            ...
        solutions/
            00001_w.txt
            00001_0.txt
            00001_1.txt
            00002_w.txt
            00002_0.txt
            00002_1.txt
            ...
"""

import os
import sys
import glob
import time
import numpy as np

# Import your utility classes/functions from utils.py
from utils import SchedulingEnv, action_helper_rollout

def find_instance_ids(problem_folder):
    """
    Scan for all files matching '*_dur.txt' inside problem_folder and
    return a sorted list of instance IDs (strings).

    E.g., if 'constrains/00001_dur.txt' and 'constrains/00002_dur.txt' exist,
    this returns ['00001', '00002'].
    """
    pattern = os.path.join(problem_folder, "*_dur.txt")
    dur_files = glob.glob(pattern)
    instance_ids = []
    for fp in dur_files:
        fname = os.path.basename(fp)
        if fname.endswith("_dur.txt"):
            instance_id = fname[:-len("_dur.txt")]
            instance_ids.append(instance_id)
    try:
        return sorted(instance_ids, key=lambda x: int(x))
    except ValueError:
        return sorted(instance_ids)

def load_solution_files(solution_folder, instance_id, num_robots):
    """
    Attempt to load:
      - global order:   instance_id + '_w.txt'
      - per-robot lists: instance_id + '_0.txt', instance_id + '_1.txt', ..., up to num_robots
    
    Returns:
      global_order (list of ints),
      optimals     (list of lists, length = num_robots),
      missing_flag (True if any required file is missing)
    """
    missing_flag = False
    w_path = os.path.join(solution_folder, f"{instance_id}_w.txt")
    if not os.path.isfile(w_path):
        print(f"    [ERROR] Missing global order file: {w_path}")
        return None, None, True

    global_order = np.loadtxt(w_path, dtype=np.int32).flatten().tolist()

    optimals = []
    for r in range(num_robots):
        robot_path = os.path.join(solution_folder, f"{instance_id}_{r}.txt")
        if os.path.isfile(robot_path):
            arr = np.loadtxt(robot_path, dtype=np.int32).flatten().tolist()
            optimals.append(arr)
        else:
            print(f"    [WARN] Missing robot‐{r} file: {robot_path} → using empty list")
            optimals.append([])

    return global_order, optimals, False

def test_single_instance(problem_folder, solution_folder, instance_id):
    """
    Test one instance by:
      1) Loading its 4 constraint files into SchedulingEnv.
      2) Loading solution files (global order + per‐robot lists).
      3) Re‐initializing SchedulingEnv to start fresh.
      4) Stepping through env.insert_robot(task, robot) for each task in global order.
      5) Returning True if all insertions succeed; False otherwise.
    """
    # 1) Paths to the 4 constraint files
    dur_path  = os.path.join(problem_folder, f"{instance_id}_dur.txt")
    ddl_path  = os.path.join(problem_folder, f"{instance_id}_ddl.txt")
    wait_path = os.path.join(problem_folder, f"{instance_id}_wait.txt")
    loc_path  = os.path.join(problem_folder, f"{instance_id}_loc.txt")

    # Verify all constraint files exist
    for p in (dur_path, ddl_path, wait_path, loc_path):
        if not os.path.isfile(p):
            print(f"  [ERROR] Missing constraint file: {p}")
            return False

    # 2) Initialize to get num_robots, etc.
    env = SchedulingEnv(os.path.join(problem_folder, instance_id))

    # 3) Load solution (global order + per‐robot)
    global_order, optimals, missing = load_solution_files(solution_folder, instance_id, env.num_robots)
    if missing:
        return False

    # 4) Re-initialize SchedulingEnv to “fresh” state (avoid any side-effects)
    env = SchedulingEnv(os.path.join(problem_folder, instance_id))

    # 5) Step through each task in global_order
    for idx, task_id in enumerate(global_order):
        # Find which robot holds this task in its optimals list
        assigned_robot = None
        for r, tasks_r in enumerate(optimals):
            if task_id in tasks_r:
                assigned_robot = r
                break
        if assigned_robot is None:
            # If no per-robot file lists this task, default to robot 0
            assigned_robot = 0

        success, reward, done = env.insert_robot(task_id, assigned_robot)
        if not success:
            print(f"    [Instance {instance_id}] Infeasible at step {idx+1}: Task {task_id} → Robot {assigned_robot}")
            return False

        # If done=True (episode ended) but tasks remain, that’s also a failure
        if done and (idx < len(global_order) - 1):
            print(f"    [Instance {instance_id}] “done=True” at step {idx+1}, but there are still tasks left.")
            return False

    # If we reach here, all insertions were feasible
    return True

def main():
    # ------------------------------
    # 0) Configuration: folders
    # ------------------------------
    PROBLEM_FOLDER  = os.path.join("problem_test", "constraints")
    SOLUTION_FOLDER = os.path.join("problem_test", "solutions")

    if not os.path.isdir(PROBLEM_FOLDER):
        print(f"ERROR: Problem‐instances folder not found: {PROBLEM_FOLDER}")
        sys.exit(1)
    if not os.path.isdir(SOLUTION_FOLDER):
        print(f"ERROR: Solutions folder not found: {SOLUTION_FOLDER}")
        sys.exit(1)

    # ------------------------------
    # 1) Identify all instance IDs
    # ------------------------------
    inst_ids = find_instance_ids(PROBLEM_FOLDER)
    if len(inst_ids) == 0:
        print("No '*_dur.txt' files found in:", PROBLEM_FOLDER, "\nExiting.")
        sys.exit(1)

    print(f"Found {len(inst_ids)} instance(s). Starting batch test...\n")

    # ------------------------------
    # 2) Loop over all instances
    # ------------------------------
    start_time = time.time()
    num_passed = 0
    num_failed = 0

    for count, inst_id in enumerate(inst_ids, start=1):
        print(f"Testing [{count}/{len(inst_ids)}] Instance = {inst_id} ...", end=" ", flush=True)
        success = test_single_instance(PROBLEM_FOLDER, SOLUTION_FOLDER, inst_id)
        if success:
            print("PASS")
            num_passed += 1
        else:
            print("FAIL")
            num_failed += 1

    elapsed = time.time() - start_time
    print("\n=== BATCH TEST COMPLETE ===")
    print(f"  Total instances: {len(inst_ids)}")
    print(f"  Passed:          {num_passed}")
    print(f"  Failed:          {num_failed}")
    print(f"  Elapsed time:    {elapsed:.1f} seconds")

if __name__ == "__main__":
    main()

