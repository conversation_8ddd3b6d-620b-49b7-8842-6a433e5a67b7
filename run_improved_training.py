#!/usr/bin/env python3
"""
<PERSON>ript to run the improved lr_scheduler_train.py with optimized parameters
"""

import subprocess
import sys
import os

def run_training():
    """Run the improved training script with optimized parameters"""
    
    # Check if the training data directory exists
    data_dir = "./gen/r2t20_001"
    if not os.path.exists(data_dir):
        print(f"Warning: Training data directory {data_dir} not found.")
        print("Please ensure you have the training data available.")
        return False
    
    # Create checkpoint directory if it doesn't exist
    checkpoint_dir = "./cp"
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    # Training parameters optimized for better convergence
    cmd = [
        sys.executable, "lr_scheduler_train.py",
        "--path-to-train", data_dir,
        "--num-robots", "2",
        "--train-start-no", "1",
        "--train-end-no", "100",  # Reduced for faster testing
        "--steps", "200",  # Reduced for initial testing
        "--gamma", "0.99",
        "--batch-size", "8",  # Increased from 2
        "--lr", "1e-4",  # More conservative learning rate
        "--weight-decay", "1e-6",
        "--checkpoint-interval", "20",
        "--cpsave", checkpoint_dir
    ]
    
    print("Starting improved training with the following parameters:")
    print(f"  Learning rate: 1e-4 (more conservative)")
    print(f"  Batch size: 8 (increased from 2)")
    print(f"  Training steps: 200")
    print(f"  Training samples: 1-100")
    print(f"  Checkpoint directory: {checkpoint_dir}")
    print(f"  Gradient clipping: enabled (max_norm=0.5)")
    print(f"  Reward normalization: enabled (divide by 1000)")
    print(f"  Reduced offset: 0.1 (much smaller)")
    print(f"  Loss explosion protection: enabled")
    print()
    
    try:
        # Run the training script
        subprocess.run(cmd, check=True, capture_output=False)
        print("\nTraining completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\nTraining failed with error code {e.returncode}")
        return False
    except KeyboardInterrupt:
        print("\nTraining interrupted by user")
        return False
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("IMPROVED TRAINING SCRIPT FOR MRC SCHEDULING")
    print("=" * 60)
    print()
    
    success = run_training()
    
    if success:
        print("\n" + "=" * 60)
        print("TRAINING SUMMARY")
        print("=" * 60)
        print("✓ Training completed successfully")
        print("✓ Training curves saved as 'training_curves.png'")
        print("✓ Checkpoints saved in './cp/'")
        print("✓ Look for the message 'Loss below 0.02 achieved' for early stopping")
        print("\nKey improvements implemented:")
        print("  • Increased learning rate for faster convergence")
        print("  • Larger batch size for stable gradients")
        print("  • Gradient clipping to prevent exploding gradients")
        print("  • Reward normalization for training stability")
        print("  • Reduced offset for better Q-value alignment")
        print("  • Comprehensive logging and visualization")
        print("  • Early stopping when loss < 0.02")
    else:
        print("\n" + "=" * 60)
        print("TRAINING FAILED")
        print("=" * 60)
        print("Please check the error messages above and ensure:")
        print("  • Training data is available in ./gen/r2t20_001/")
        print("  • All required dependencies are installed")
        print("  • Sufficient disk space for checkpoints")
