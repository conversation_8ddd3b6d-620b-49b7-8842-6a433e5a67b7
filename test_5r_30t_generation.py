#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for 5 robots and 30 tasks problem generation
"""

import os
import sys
import numpy as np
from datetime import datetime

# Add current directory to path
sys.path.append('.')

from utils import SchedulingEnv
from generate_saved_makespan import generate_feasible_problem

def test_single_instance():
    """Test generation of a single 5-robot 30-task instance"""
    print("🧪 Testing single instance generation...")
    
    # Generate one instance
    result = generate_feasible_problem(1)
    makespan, feasible, comp_time = result
    
    if feasible:
        print(f"✅ Successfully generated instance 1")
        print(f"   Makespan: {makespan:.2f}")
        print(f"   Feasible: {feasible}")
        print(f"   Computation time: {comp_time:.3f}s")
        
        # Test loading the generated instance
        constraints_path = "./problem_instances_5r_30t/constraints/00001"
        if os.path.exists(constraints_path + "_dur.txt"):
            env = SchedulingEnv(constraints_path)
            print(f"   Loaded environment: {env.num_tasks} tasks, {env.num_robots} robots")
            
            # Verify dimensions
            assert env.num_tasks == 30, f"Expected 30 tasks, got {env.num_tasks}"
            assert env.num_robots == 5, f"Expected 5 robots, got {env.num_robots}"
            
            print("   ✅ Environment loaded successfully with correct dimensions")
            return True
        else:
            print("   ❌ Generated files not found")
            return False
    else:
        print(f"   ❌ Failed to generate feasible instance")
        print(f"   Computation time: {comp_time:.3f}s")
        return False

def test_batch_generation():
    """Test generation of multiple instances"""
    print("\n🧪 Testing batch generation (5 instances)...")
    
    success_count = 0
    total_time = 0.0
    
    for i in range(2, 7):  # Generate instances 2-6
        result = generate_feasible_problem(i)
        makespan, feasible, comp_time = result
        total_time += comp_time
        
        if feasible:
            success_count += 1
            print(f"   ✅ Instance {i}: makespan={makespan:.2f}, time={comp_time:.3f}s")
        else:
            print(f"   ❌ Instance {i}: failed, time={comp_time:.3f}s")
    
    print(f"\n📊 Batch results:")
    print(f"   Success rate: {success_count}/5 ({success_count/5*100:.1f}%)")
    print(f"   Average time per instance: {total_time/5:.3f}s")
    
    return success_count > 0

def test_training_data_loading():
    """Test if the training script can load the generated data"""
    print("\n🧪 Testing training data loading...")
    
    try:
        # Import the new data loading function
        from lr_scheduler_train import fill_demo_data_5r_30t
        
        constraints_folder = "./problem_instances_5r_30t/constraints"
        solutions_folder = "./problem_instances_5r_30t/solutions"
        
        if not os.path.exists(constraints_folder) or not os.path.exists(solutions_folder):
            print("   ⚠️  Data folders not found, skipping training data test")
            return True
        
        # Try to load a small batch
        memory = fill_demo_data_5r_30t(constraints_folder, solutions_folder, 1, 2, 0.99)
        
        if len(memory) > 0:
            print(f"   ✅ Successfully loaded {len(memory)} transitions from training data")
            return True
        else:
            print("   ❌ No transitions loaded from training data")
            return False
            
    except Exception as e:
        print(f"   ❌ Error loading training data: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing 5-robot 30-task problem generation system\n")
    
    # Ensure output directories exist
    os.makedirs("./problem_instances_5r_30t/constraints", exist_ok=True)
    os.makedirs("./problem_instances_5r_30t/solutions", exist_ok=True)
    
    start_time = datetime.now()
    
    # Run tests
    test1_passed = test_single_instance()
    test2_passed = test_batch_generation()
    test3_passed = test_training_data_loading()
    
    end_time = datetime.now()
    total_time = (end_time - start_time).total_seconds()
    
    # Summary
    print(f"\n📋 Test Summary:")
    print(f"   Single instance generation: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Batch generation: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   Training data loading: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    print(f"   Total test time: {total_time:.2f}s")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 All tests passed! The system is ready for 5-robot 30-task problems.")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the configuration.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
