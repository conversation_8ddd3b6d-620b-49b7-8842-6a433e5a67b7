import os
import numpy as np
import gurobipy as gp
from gurobipy import GRB
from datetime import datetime

from utils import SchedulingEnv
from benchmark.JohnsonUltra import johnsonU

# Configuration
NUM_INSTANCES = 700
NUM_TASKS = 5
NUM_ROBOTS = 2
OUTPUT_FOLDER = "problem_instances_test"
CONSTRAINTS_FOLDER = os.path.join(OUTPUT_FOLDER, "constraints")
SOLUTIONS_FOLDER = os.path.join(OUTPUT_FOLDER, "solutions")
T = 5  # Time multiplier for deadlines

os.makedirs(CONSTRAINTS_FOLDER, exist_ok=True)
os.makedirs(SOLUTIONS_FOLDER, exist_ok=True)


def detect_negative_cycle(G):
    try:
        _, _ = johnsonU(G)
        return False
    except Exception:
        return True


def solve_makespan(env):
    model = gp.Model("makespan")
    model.setParam("OutputFlag", 0)

    tasks = range(1, NUM_TASKS + 1)
    robots = range(NUM_ROBOTS)

    # Decision variables
    start_times = model.addVars(tasks, lb=0, vtype=GRB.CONTINUOUS, name="start")
    x = model.addVars(tasks, robots, vtype=GRB.BINARY, name="assign")

    makespan = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="makespan")
    model.setObjective(makespan, GRB.MINIMIZE)

    # Each task assigned exactly one robot
    for t in tasks:
        model.addConstr(gp.quicksum(x[t, r] for r in robots) == 1, f"assign_{t}")

    # Duration constraints
    for t in tasks:
        model.addConstr(
            start_times[t] + gp.quicksum(x[t, r] * env.dur[t - 1, r] for r in robots) <= makespan,
            f"dur_{t}"
        )

    # Deadline constraints
    for task_id, deadline in env.ddl:
        model.addConstr(start_times[task_id] <= deadline, f"deadline_{task_id}")

    # Waiting constraints
    for ti, tj, wait_time in env.wait:
        model.addConstr(start_times[ti] + wait_time <= start_times[tj], f"wait_{ti}_{tj}")

    model.optimize()

    if model.status == GRB.OPTIMAL:
        assignments = {r: [] for r in robots}
        for t in tasks:
            for r in robots:
                if x[t, r].X > 0.5:
                    assignments[r].append(t)
                    break
        # Sort tasks per robot by start time for correct insertion order
        for r in robots:
            assignments[r].sort(key=lambda task: start_times[task].X)
        return model.objVal, assignments
    else:
        return None, None


def partial_schedule_feasibility_check(env, assignments):
    # Reset partial schedules
    env.partials = [np.zeros(1, dtype=np.int32) for _ in range(env.num_robots)]
    env.partialw = np.zeros(1, dtype=np.int32)

    # Insert tasks in the order assigned per robot
    for r in range(env.num_robots):
        for task in assignments[r]:
            success, _, _ = env.insert_robot(task, r)
            if not success:
                return False
    return True


def generate_feasible_problem(instance_id):
    max_attempts = 100
    attempt = 0

    while attempt < max_attempts:
        attempt += 1

        durations = np.random.randint(1, 11, size=(NUM_TASKS, NUM_ROBOTS))

        env = SchedulingEnv.__new__(SchedulingEnv)
        env.dur = durations
        env.ddl = np.empty((0, 2), dtype=int)
        env.wait = np.empty((0, 3), dtype=int)
        env.loc = np.column_stack((
            np.random.randint(1, NUM_ROBOTS + 1, size=NUM_TASKS),
            np.random.randint(1, NUM_ROBOTS + 1, size=NUM_TASKS)
        ))
        env.num_tasks = NUM_TASKS
        env.num_robots = NUM_ROBOTS
        env.M = NUM_TASKS * 10.0
        env.C = 3.0
        env.max_deadline = NUM_TASKS * 10
        env.partials = [np.zeros(1, dtype=np.int32) for _ in range(NUM_ROBOTS)]
        env.partialw = np.zeros(1, dtype=np.int32)

        env.g = env.initialize_STN()

        # Initialize min_makespan attribute to avoid errors in insert_robot
        success, min_makespan = env.check_consistency_makespan()
        if not success:
            print(f"Initial STN infeasible for instance {instance_id}, attempt {attempt}")
            continue
        env.min_makespan = min_makespan

        # Add deadlines incrementally, checking STN feasibility
        num_deadlines = max(1, int(0.25 * NUM_TASKS))
        deadlines = []
        discarded_deadlines = 0
        deadline_attempts = 0
        while len(deadlines) < num_deadlines and deadline_attempts < 200:
            deadline_attempts += 1
            task_id = np.random.randint(1, NUM_TASKS + 1)
            max_time = NUM_TASKS * T - durations[task_id - 1].max()
            if max_time <= 5:
                continue
            deadline = np.random.randint(5, max_time)
            fi = f'f{task_id:03d}'
            env.g.add_edge('s000', fi, weight=deadline)
            if detect_negative_cycle(env.g):
                env.g.remove_edge('s000', fi)
                discarded_deadlines += 1
            else:
                deadlines.append([task_id, deadline])
        deadlines = np.array(deadlines)

        # Add waiting constraints incrementally
        num_wait_constraints = max(1, int(0.25 * NUM_TASKS))
        wait_constraints = []
        discarded_waits = 0
        wait_attempts = 0
        while len(wait_constraints) < num_wait_constraints and wait_attempts < 400:
            wait_attempts += 1
            i, j = np.random.choice(range(1, NUM_TASKS + 1), size=2, replace=False)
            if i >= j:
                continue
            wait_time = np.random.randint(1, 11)
            si = f's{i:03d}'
            fj = f'f{j:03d}'
            env.g.add_edge(si, fj, weight=-wait_time)
            if detect_negative_cycle(env.g):
                env.g.remove_edge(si, fj)
                discarded_waits += 1
            else:
                wait_constraints.append([i, j, wait_time])
        wait_constraints = np.array(wait_constraints)

        if detect_negative_cycle(env.g):
            print(f"Negative cycle detected after adding constraints, instance {instance_id}, attempt {attempt}")
            continue

        # Solve makespan with Gurobi
        optimal_makespan, gurobi_assignments = solve_makespan(env)
        if optimal_makespan is None or gurobi_assignments is None:
            print(f"Gurobi failed to find feasible solution, instance {instance_id}, attempt {attempt}")
            continue

        # Partial schedule feasibility check using Gurobi assignments
        if not partial_schedule_feasibility_check(env, gurobi_assignments):
            print(f"Partial schedule feasibility check failed, instance {instance_id}, attempt {attempt}")
            continue

        # Save constraints
        prefix_c = os.path.join(CONSTRAINTS_FOLDER, f"{instance_id:05d}")
        np.savetxt(f"{prefix_c}_dur.txt", durations, fmt="%d")
        np.savetxt(f"{prefix_c}_ddl.txt", deadlines, fmt="%d")
        np.savetxt(f"{prefix_c}_wait.txt", wait_constraints, fmt="%d")
        np.savetxt(f"{prefix_c}_loc.txt", env.loc, fmt="%d")

        # Save solutions
        prefix_s = os.path.join(SOLUTIONS_FOLDER, f"{instance_id:05d}")

        # Save global solution task order as _w.txt (concatenate tasks by robot in order)
        task_order = []
        for r in range(NUM_ROBOTS):
            task_order.extend(gurobi_assignments[r])
        task_order = np.array(task_order).reshape(-1, 1)
        np.savetxt(f"{prefix_s}_w.txt", task_order, fmt="%d")

        # Save individual robot schedules
        for r in range(NUM_ROBOTS):
            robot_tasks = np.array(gurobi_assignments[r]).reshape(-1, 1) if gurobi_assignments[r] else np.empty((0, 1), dtype=int)
            np.savetxt(f"{prefix_s}_{r}.txt", robot_tasks, fmt="%d")

        print(f"✅ Instance {instance_id} generated with makespan {optimal_makespan}, "
              f"Attempts: {attempt}, Discarded Deadlines: {discarded_deadlines}, Discarded Waits: {discarded_waits}")

        return True

    print(f"❌ Failed to generate instance {instance_id} after {max_attempts} attempts.")
    return False


if __name__ == "__main__":
    start_time = datetime.now()
    print(f"🚀 Generating {NUM_INSTANCES} problem instances...")

    success_count = 0
    for instance_id in range(1, NUM_INSTANCES + 1):
        if generate_feasible_problem(instance_id):
            success_count += 1

    end_time = datetime.now()
    print(f"✅ Successfully generated {success_count} problem instances.")
    print(f"⏳ Time taken: {end_time - start_time}")

