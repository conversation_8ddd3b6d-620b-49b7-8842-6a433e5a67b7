
import torch
import copy
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt

from utils import SchedulingEnv
from benchmark.edfutils import RobotTeam
from utils import build_hetgraph
from utils import hetgraph_node_helper
from hetnet import ScheduleNet4Layer


fname = './data/00374'
print(fname)

# initialize the scheduling enviroment with data files
env = SchedulingEnv(fname)

# initialize the robot teams
robots = RobotTeam(env.num_robots)

# size of location map, 3x3 is used
map_width = 3
print('Duration\n', env.dur)
print('Deadline Constraints\n', env.ddl)
print('Wait Constraints\n', env.wait)
print('Locaction Constraints\n', env.loc)
# partials schedules are initialized with task 0 (marker for s0: time origin)
print(env.partialw)
print(env.partials)
# The STN
print(env.g)
print(env.g.nodes())
# The simplified STN serves as a base to build the heterogenous graph
# representation used in ScheduleNet
print(env.halfDG)
print(env.halfDG.nodes())

decision_step = 0
# start with time origin
t = 0
robot_chosen = robots.pick_robot_by_min_dur(t, env, 'v1', exclude = [])
# shift idx to start with 1
print('Robot #', robot_chosen+1, 'is picked')
unsch_tasks = np.array(env.get_unscheduled_tasks(), dtype=np.int64)
print('Unscheduled tasks\n', unsch_tasks)
valid_tasks = np.array(env.get_valid_tasks(t), dtype=np.int64)
print('After filtering based on STN\n', valid_tasks)


g = build_hetgraph(env.halfDG, env.num_tasks, env.num_robots, env.dur,
                                map_width, np.array(env.loc, dtype=np.int64),
                                1.0, env.partials, unsch_tasks, robot_chosen,
                                valid_tasks)

print(g)


feat_dict = hetgraph_node_helper(env.halfDG.number_of_nodes(),
                                              env.partialw,
                                              env.partials, env.loc, env.dur,
                                              map_width, env.num_robots,
                                              len(valid_tasks))

feat_dict_tensor = {}
for key in feat_dict:
    feat_dict_tensor[key] = torch.Tensor(feat_dict[key])
for key in feat_dict_tensor:
    print(key, feat_dict_tensor[key].shape)
    
    
    
print(feat_dict_tensor['state'])

# Q-value nodes are initialized as zero
print(feat_dict_tensor['value'])


device = torch.device('cpu')

in_dim = {'task': 6,
          'loc': 1,
          'robot': 1,
          'state': 4,
          'value': 1
          }

hid_dim = {'task': 64,
           'loc': 64,
           'robot': 64,
           'state': 64,
           'value': 64
           }

out_dim = {'task': 32,
          'loc': 32,
          'robot': 32,
          'state': 32,
          'value': 1
          }

cetypes = [('task', 'temporal', 'task'),
           ('task', 'located_in', 'loc'),('loc', 'near', 'loc'),
           ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
           ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
           ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
           ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
           ('state', 'sto', 'value'), ('value', 'vto', 'value'),
           ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')]

num_heads = 8

policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, num_heads).to(device)
policy_net.eval()
print(policy_net)


result = policy_net(g, feat_dict_tensor)

print(result.keys())


# Lx1
q_s_a = result['value']
pre = q_s_a[:,0].data.cpu().numpy()

print('Q Value predictions:', q_s_a)



state_graphs = []
partials = []
partialw = []
actions_task = []
actions_robot = []
rewards = []
terminates = []
        
state_graphs.append(copy.deepcopy(env.halfDG))
partials.append(copy.deepcopy(env.partials))
partialw.append(copy.deepcopy(env.partialw))
terminates.append(False)
for i in range(env.num_tasks):
            for j in range(env.num_robots):
                if optimalw[i] in optimals[j]:
                    rj = j
                    break
            
            act_chosen = optimalw[i]
            print('step: %d, action: [%d, %d]' % (t, act_chosen, rj))
            
            # insert the node, update state, and get reward
            rt, reward, done = env.insert_robot(act_chosen, rj)
            print(rt, reward, done, env.min_makespan)
            
            state_graphs.append(copy.deepcopy(env.halfDG))
            partials.append(copy.deepcopy(env.partials))
            partialw.append(copy.deepcopy(env.partialw))
            actions_task.append(act_chosen)
            actions_robot.append(rj)
            rewards.append(reward)
            terminates.append(done)
            
    
