#!/usr/bin/env python3
"""
Generate simple training data for 30-task instances without requiring <PERSON><PERSON>bi
This creates feasible instances with simple greedy solutions for testing
"""

import os
import numpy as np
from utils import SchedulingEnv

def generate_simple_solution(env):
    """Generate a simple greedy solution for testing"""
    # Simple greedy: assign tasks to robots in round-robin fashion
    # with earliest deadline first
    
    # Get all tasks with their deadlines
    task_deadlines = []
    for i in range(env.num_tasks):
        # Find deadline for this task
        deadline = env.max_deadline  # Default to max if no specific deadline
        for ddl_constraint in env.ddl:
            if ddl_constraint[0] == i + 1:  # Task IDs are 1-indexed
                deadline = ddl_constraint[1]
                break
        task_deadlines.append((i + 1, deadline))  # (task_id, deadline)
    
    # Sort by deadline
    task_deadlines.sort(key=lambda x: x[1])
    
    # Assign to robots in round-robin
    robot_assignments = [[] for _ in range(env.num_robots)]
    for i, (task_id, _) in enumerate(task_deadlines):
        robot_id = i % env.num_robots
        robot_assignments[robot_id].append(task_id)
    
    # Create global task order
    global_order = []
    for robot_tasks in robot_assignments:
        global_order.extend(robot_tasks)
    
    return robot_assignments, global_order

def create_training_instance(instance_id, output_dir):
    """Create a single training instance"""
    NUM_TASKS = 30
    NUM_ROBOTS = 5
    
    # Generate feasible durations (shorter for feasibility)
    durations = np.random.randint(1, 6, size=(NUM_TASKS, NUM_ROBOTS))
    
    # Generate fewer constraints for better feasibility
    num_deadlines = max(1, int(0.1 * NUM_TASKS))  # 10% of tasks have deadlines
    num_wait_constraints = max(1, int(0.05 * NUM_TASKS))  # 5% have wait constraints
    
    # Create deadline constraints
    deadline_tasks = np.random.choice(NUM_TASKS, size=num_deadlines, replace=False) + 1
    deadlines = []
    for task in deadline_tasks:
        # Set deadline to be achievable (task duration * 15)
        min_duration = min(durations[task-1])
        deadline_time = min_duration * 15
        deadlines.append([task, deadline_time])
    deadlines = np.array(deadlines)
    
    # Create wait constraints
    wait_constraints = []
    for _ in range(num_wait_constraints):
        task1 = np.random.randint(1, NUM_TASKS + 1)
        task2 = np.random.randint(1, NUM_TASKS + 1)
        if task1 != task2:
            wait_time = np.random.randint(1, 4)  # Short wait times
            wait_constraints.append([task1, task2, wait_time])
    
    if wait_constraints:
        wait_constraints = np.array(wait_constraints)
    else:
        wait_constraints = np.empty((0, 3), dtype=int)
    
    # Generate location constraints
    locations = np.random.randint(1, 3, size=(NUM_TASKS, 2))
    
    # Save constraint files
    constraints_dir = os.path.join(output_dir, "constraints")
    os.makedirs(constraints_dir, exist_ok=True)
    
    prefix = os.path.join(constraints_dir, f"{instance_id:05d}")
    np.savetxt(f"{prefix}_dur.txt", durations, fmt="%d")
    np.savetxt(f"{prefix}_ddl.txt", deadlines, fmt="%d")
    np.savetxt(f"{prefix}_wait.txt", wait_constraints, fmt="%d")
    np.savetxt(f"{prefix}_loc.txt", locations, fmt="%d")
    
    # Create environment and generate solution
    env = SchedulingEnv(prefix)
    robot_assignments, global_order = generate_simple_solution(env)
    
    # Save solution files
    solutions_dir = os.path.join(output_dir, "solutions")
    os.makedirs(solutions_dir, exist_ok=True)
    
    sol_prefix = os.path.join(solutions_dir, f"{instance_id:05d}")
    
    # Save global order
    np.savetxt(f"{sol_prefix}_w.txt", np.array(global_order).reshape(-1, 1), fmt="%d")
    
    # Save per-robot assignments
    for robot_id, tasks in enumerate(robot_assignments):
        if tasks:
            np.savetxt(f"{sol_prefix}_{robot_id}.txt", np.array(tasks).reshape(-1, 1), fmt="%d")
        else:
            # Create empty file for robots with no tasks
            np.savetxt(f"{sol_prefix}_{robot_id}.txt", np.array([]).reshape(-1, 1), fmt="%d")
    
    return True

def main():
    """Generate training data for 30-task instances"""
    print("🚀 Generating simple training data for 30-task instances...")
    
    output_dir = "problem_instances_train_30"
    num_instances = 1000  # Generate 20 instances for testing
    
    # Clean up existing directory
    if os.path.exists(output_dir):
        import shutil
        shutil.rmtree(output_dir)
    
    os.makedirs(output_dir, exist_ok=True)
    
    successful_instances = 0
    
    for i in range(1, num_instances + 1):
        try:
            print(f"Generating instance {i}/{num_instances}...", end='\r')
            success = create_training_instance(i, output_dir)
            if success:
                successful_instances += 1
        except Exception as e:
            print(f"\n❌ Error generating instance {i}: {e}")
            continue
    
    print(f"\n✅ Successfully generated {successful_instances}/{num_instances} instances")
    print(f"📁 Data saved to: {output_dir}/")
    print(f"   - Constraints: {output_dir}/constraints/")
    print(f"   - Solutions: {output_dir}/solutions/")
    
    # Test loading one instance
    print("\n🔍 Testing generated data...")
    try:
        test_prefix = os.path.join(output_dir, "constraints", "00001")
        env = SchedulingEnv(test_prefix)
        print(f"✅ Test instance loaded successfully!")
        print(f"   - Tasks: {env.num_tasks}, Robots: {env.num_robots}")
        print(f"   - STN nodes: {env.g.number_of_nodes()}, edges: {env.g.number_of_edges()}")
        
        # Check if solution files exist
        sol_prefix = os.path.join(output_dir, "solutions", "00001")
        if os.path.exists(f"{sol_prefix}_w.txt"):
            global_order = np.loadtxt(f"{sol_prefix}_w.txt", dtype=int).flatten()
            print(f"   - Solution length: {len(global_order)}")
            print(f"   - First 10 tasks: {global_order[:10]}")
        
    except Exception as e:
        print(f"❌ Error testing generated data: {e}")
        return
    
    print("\n🎉 Training data generation completed!")
    print("\nNext steps:")
    print("1. Run training: python3 train_30_tasks.py")
    print("2. Or use automated training: python3 run_improved_training.py")

if __name__ == "__main__":
    main()
