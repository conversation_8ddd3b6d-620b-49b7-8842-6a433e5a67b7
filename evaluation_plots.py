#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
plot_optimality_ratios_adjusted.py

Reads five CSV files (gurobi.csv, edf.csv, tercio.csv, random.csv, ssan.csv),
each of which must have exactly these four column headers (tab- or comma-separated):

    instance_id    makespan    feasible    runtime

- In gurobi.csv: 'makespan' = z_opt whenever feasible == True.
- In edf/tercio/random/ssan: we rename "makespan" → "z_model", and force z_model=NaN 
  wherever feasible == False.

We then drop any instance not solved by <PERSON><PERSON><PERSON>, read "{instance_id}_dur.txt" to count tasks,
bucket into three size‐ranges (small/medium/large), compute M₁(α) for α ∈ [1.00, 2.00]
in steps of 0.05, and finally plot three side‐by‐side subplots (small | medium | large).

Usage:
    python3 plot_optimality_ratios_adjusted.py \
      --csv-folder ./csv_outputs \
      --path-constraints ./problem_instances_test/constraints \
      --output-file optimality_curves.png
"""

import os
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def get_num_tasks_from_dur_file(dur_file_path):
    """
    Read the '{instance_id}_dur.txt' file and return the number of tasks (# of rows).
    """
    try:
        arr = np.loadtxt(dur_file_path, dtype=int)
    except Exception as e:
        raise RuntimeError(f"Error reading {dur_file_path}: {e}")

    # If it’s a single row, arr.ndim might be 1; treat that as 1 task.
    if arr.ndim == 1:
        return 1
    else:
        return arr.shape[0]

def get_size_category(num_tasks):
    """
    Assign a size‐category string based on number of tasks.
    Since all of your test instances have exactly 5 tasks, we put:
      - "small":  1 ≤ num_tasks ≤ 5
      - "medium": 6 ≤ num_tasks ≤ 10
      - "large": 11 ≤ num_tasks ≤ 20
    Any other task‐count → None (ignored).

    Adjust these thresholds if your future instances have different sizes.
    """
    if 1 <= num_tasks <= 5:
        return "small"
    elif 6 <= num_tasks <= 10:
        return "medium"
    elif 11 <= num_tasks <= 20:
        return "large"
    else:
        return None

def compute_M1_curve(df_solver, alpha_values):
    """
    Given a DataFrame for one solver, which must contain columns:
      - instance_id (str)
      - z_opt       (float)
      - z_model     (float or NaN)
      - feasible    (bool)
      - num_tasks   (int)

    Compute M₁(α) for α in alpha_values.  Return a dict:
      {
        "small":  [M1(α₁), M1(α₂), ..., M1(αₙ)],
        "medium": [...],
        "large":  [...]
      }

    Where:
      M₁(α) = 100 * ( |{i : feasible=True & ratio[i] ≤ α}| ) 
                      / ( total # of feasible instances in that size‐range )

    If a size‐range has zero feasible instances, return 0.0 for all α in that bucket.
    """
    # 1) Drop any row with NaN z_opt (i.e. Gurobi did not solve that instance feasibly)
    df_valid = df_solver.dropna(subset=["z_opt"]).copy()

    # 2) Force df_valid["feasible"] to be boolean
    df_valid["feasible"] = df_valid["feasible"].astype(bool)

    # 3) Only keep rows where feasible == True (for that solver)
    df_valid = df_valid[df_valid["feasible"] == True].copy()

    # 4) If no feasible rows remain, return zero‐curves
    if df_valid.shape[0] == 0:
        return {
            "small":  [0.0] * len(alpha_values),
            "medium": [0.0] * len(alpha_values),
            "large":  [0.0] * len(alpha_values)
        }

    # 5) Compute ratio = z_model / z_opt
    #    Note: z_model is NaN wherever solver declared infeasible ⇒ those will be dropped next
    df_valid["ratio"] = df_valid["z_model"].values / df_valid["z_opt"].values

    # 6) Assign size categories
    df_valid["size_cat"] = df_valid["num_tasks"].apply(get_size_category)

    # 7) Build dictionary of M₁(α) for each size category
    result = {"small": [], "medium": [], "large": []}

    for size_cat in ["small", "medium", "large"]:
        sub = df_valid[df_valid["size_cat"] == size_cat]
        total = sub.shape[0]
        if total == 0:
            # no feasible instances in this bucket
            result[size_cat] = [0.0] * len(alpha_values)
        else:
            ratios = sub["ratio"].values
            curve = []
            for α in alpha_values:
                count_leq = np.count_nonzero(ratios <= α)
                pct = 100.0 * count_leq / total
                curve.append(pct)
            result[size_cat] = curve

    return result

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--csv-folder",
        type=str,
        required=True,
        help="Path to folder containing: gurobi.csv, edf.csv, tercio.csv, random.csv, ssan.csv"
    )
    parser.add_argument(
        "--path-constraints",
        type=str,
        required=True,
        help="Path to folder containing all *_dur.txt (only need *_dur.txt to count tasks)"
    )
    parser.add_argument(
        "--output-file",
        type=str,
        default="optimality_curves.png",
        help="Where to save the resulting figure (PNG)."
    )
    args = parser.parse_args()

    # 1) Load gurobi.csv  (z_opt)
    gurobi_path = os.path.join(args.csv_folder, "gurobi.csv")
    if not os.path.isfile(gurobi_path):
        raise FileNotFoundError(f"Cannot find {gurobi_path}. Please put gurobi.csv in {args.csv_folder}.")

    df_gurobi = pd.read_csv(gurobi_path, dtype={"instance_id": str})
    # Verify required columns
    for col in ["instance_id", "makespan", "feasible", "runtime"]:
        if col not in df_gurobi.columns:
            raise RuntimeError(f"gurobi.csv must contain the column '{col}'")

    # Build dictionary of z_opt for every instance that Gurobi solved feasibly
    z_opt_dict = {}
    for _, row in df_gurobi.iterrows():
        inst = row["instance_id"]
        z_val = row["makespan"]
        feas = row["feasible"]
        # Coerce ‘feasible’ to bool, then only record z_opt if True and not NaN
        is_feas = (str(feas).strip().lower() in ["1","true","yes","t"]) or (bool(feas) is True)
        if is_feas and (not pd.isna(z_val)):
            z_opt_dict[inst] = float(z_val)

    if len(z_opt_dict) == 0:
        raise RuntimeError("No feasible entries found in gurobi.csv; cannot proceed.")

    # 2) Load the other four solver CSVs: edf.csv, tercio.csv, random.csv, ssan.csv
    solver_names = ["edf", "tercio", "random", "ssan"]
    dfs = {}
    for solver in solver_names:
        path = os.path.join(args.csv_folder, f"{solver}.csv")
        if not os.path.isfile(path):
            raise FileNotFoundError(f"Cannot find {path}. Expected '{solver}.csv' in {args.csv_folder}")
        df = pd.read_csv(path, dtype={"instance_id": str})
        for col in ["instance_id", "makespan", "feasible", "runtime"]:
            if col not in df.columns:
                raise RuntimeError(f"{solver}.csv must contain the column '{col}'")
        # Rename "makespan" → "z_model"
        df = df.rename(columns={"makespan": "z_model"})
        # Convert z_model to numeric, forcing any invalid strings → NaN
        df["z_model"] = pd.to_numeric(df["z_model"], errors="coerce")
        # Force z_model = NaN wherever feasible == False
        df["feasible"] = df["feasible"].astype(str).str.strip().str.lower().map(
            {"true": True, "1": True, "t": True, "yes": True, "false": False, "0": False, "f": False, "no": False}
        )
        df.loc[df["feasible"] == False, "z_model"] = np.nan
        dfs[solver] = df

    # 3) For each solver‐DataFrame, add a column "z_opt" (look up from z_opt_dict).
    #    If an instance_id is not in z_opt_dict, set z_opt = NaN → drop that row afterward.
    for solver, df in dfs.items():
        zopts = []
        for _, row in df.iterrows():
            inst = row["instance_id"]
            zopts.append(z_opt_dict.get(inst, np.nan))
        df["z_opt"] = zopts
        # Drop rows where z_opt is NaN (i.e. Gurobi did not solve them feasibly)
        df = df.dropna(subset=["z_opt"]).copy()
        dfs[solver] = df.reset_index(drop=True)

    # 4) Count num_tasks per instance by reading "{instance_id}_dur.txt" under --path-constraints
    print("Counting # of tasks for each solver‐DataFrame …")
    for solver, df in dfs.items():
        num_tasks_list = []
        for _, row in df.iterrows():
            inst = row["instance_id"]
            dur_path = os.path.join(args.path_constraints, f"{inst}_dur.txt")
            if not os.path.isfile(dur_path):
                raise FileNotFoundError(f"Cannot find {dur_path} for instance {inst}")
            ntasks = get_num_tasks_from_dur_file(dur_path)
            num_tasks_list.append(ntasks)
        df["num_tasks"] = num_tasks_list
        dfs[solver] = df

    # 5) Compute M₁(α) curves for each solver over α = [1.00, 1.05, …, 2.00]
    alpha_vals = np.round(np.arange(1.00, 2.0001, 0.05), 2)
    curves = {}
    for solver in solver_names:
        print(f"Computing M₁(α) for solver '{solver}' …")
        curves[solver] = compute_M1_curve(dfs[solver], alpha_vals)

    # 6) Prepare to plot three side‐by‐side subplots (small | medium | large)
    fig, axes = plt.subplots(1, 3, figsize=(18, 5), sharey=True)
    size_titles = {
        "small":  "Small Problems (1–5 tasks)",
        "medium": "Medium Problems (6–10 tasks)",
        "large":  "Large Problems (11–20 tasks)"
    }
    solver_styles = {
        "edf":    {"color": "#FF7F0E", "marker": "o",  "label": "EDF"},
        "tercio": {"color": "#2CA02C", "marker": "s",  "label": "Tercio"},
        "random": {"color": "#9467BD", "marker": "X",  "label": "Random"},
        "ssan":   {"color": "#1F77B4", "marker": "v",  "label": "SSAN"},
        "gurobi": {"color": "#D62728", "marker": "d",  "label": "Gurobi (opt)"}
    }

    # We also need Gurobi’s own curve.  For that, rebuild a small DataFrame of gurobi‐solved instances
    df_g_opt = df_gurobi.copy()
    # Keep only those instance_ids that appear in z_opt_dict:
    df_g_opt = df_g_opt[df_g_opt["instance_id"].isin(z_opt_dict.keys())].copy()

    # Count num_tasks for Gurobi’s DataFrame as well:
    num_tasks_list = []
    for _, row in df_g_opt.iterrows():
        inst = row["instance_id"]
        dur_path = os.path.join(args.path_constraints, f"{inst}_dur.txt")
        num_tasks_list.append(get_num_tasks_from_dur_file(dur_path))
    df_g_opt["num_tasks"] = num_tasks_list
    # Only keep the ones Gurobi marked feasible (again, drop any row where 'feasible' is not True)
    df_g_opt["feasible"] = df_g_opt["feasible"].astype(str).str.strip().str.lower().map(
        {"true": True, "1": True, "t": True, "yes": True, "false": False, "0": False, "f": False, "no": False}
    )
    df_g_opt = df_g_opt[df_g_opt["feasible"] == True].copy()

    for idx, size_cat in enumerate(["small", "medium", "large"]):
        ax = axes[idx]
        ax.set_title(size_titles[size_cat], fontsize=14)
        ax.set_xlabel("Optimality Ratio α", fontsize=12)
        if idx == 0:
            ax.set_ylabel("Problems Solved (%)", fontsize=12)

        # 6a) Plot each non‐Gurobi solver
        for solver in solver_names:
            yvals = curves[solver][size_cat]
            style = solver_styles[solver]
            ax.plot(
                alpha_vals,
                yvals,
                marker=style["marker"],
                color=style["color"],
                label=style["label"],
                linewidth=2,
                markersize=6,
                alpha=0.8
            )

        # 6b) Now plot Gurobi’s “perfect” curve
        sub_g = df_g_opt[df_g_opt["num_tasks"].apply(get_size_category) == size_cat]
        total_g = sub_g.shape[0]
        if total_g == 0:
            gurobi_curve = [0.0] * len(alpha_vals)
        else:
            # For α ≥ 1.0 (our array starts at 1.0), Gurobi solves 100% at or below α=1.0
            gurobi_curve = [100.0] * len(alpha_vals)

        gstyle = solver_styles["gurobi"]
        ax.plot(
            alpha_vals,
            gurobi_curve,
            marker=gstyle["marker"],
            color=gstyle["color"],
            label=gstyle["label"],
            linewidth=2,
            markersize=6,
            alpha=0.8
        )

        ax.set_xlim(1.0, 2.0)
        ax.set_ylim(0, 100)
        ax.set_xticks(np.arange(1.0, 2.01, 0.2))
        ax.grid(True, linestyle="--", alpha=0.4)

    # Put a shared legend below the three subplots
    handles, labels = axes[-1].get_legend_handles_labels()
    fig.legend(
        handles,
        labels,
        loc="lower center",
        ncol=len(solver_styles),
        fontsize=12,
        frameon=False,
        bbox_to_anchor=(0.5, -0.05)
    )

    plt.tight_layout(rect=[0, 0.05, 1, 1])
    plt.savefig(args.output_file, dpi=300, bbox_inches="tight")
    print(f"\n✅ Saved plot as '{args.output_file}'")
    plt.show()


if __name__ == "__main__":
    main()

