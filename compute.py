#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
compute_metrics.py

Reads the CSVs produced by makespan_for_each.py (one CSV per solver),
merges them on instance_id, and computes/renders the following metrics:

  - % Infeasible
  - Average Runtime (and percentiles)
  - Average Gap (%)    = mean[(z_solver - z_opt)/z_opt * 100] on feasible instances
  - M1(α)             = % of instances with z_solver ≤ α * z_opt, for α∈[1.0,2.0]
  - (Optional) Weighted‐sum objective

Produces:
  - A single PDF/PNG folder of plots
  - A summary CSV with all aggregated metrics

Usage:
  python3 compute_metrics.py \
    --csv-folder ./csv_outputs \
    --num-instances 1000 \
    --output-folder ./eval_results
"""

import os
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

SOLVER_NAMES = ["gurobi", "edf", "tercio", "random", "ssan"]


def load_all_csvs(csv_folder, num_instances):
    """
    Reads, in order, makespans_{solver}.csv for solver in SOLVER_NAMES.
    Returns a dict: { solver_name: DataFrame }.
    Each DataFrame must have columns: [instance_id, makespan, feasible, runtime].
    If 'instance_id' is not literally present, assume the first column is the instance ID.
    """
    dfs = {}
    for solver in SOLVER_NAMES:
        path = os.path.join(csv_folder, f"makespans_{solver}.csv")
        if not os.path.isfile(path):
            raise FileNotFoundError(f"Expected CSV not found: {path}")

        # 1) Load CSV into DataFrame
        df = pd.read_csv(path)

        # 2) If there is no 'instance_id' column, rename the very first column to 'instance_id'
        if "instance_id" not in df.columns:
            first_col = df.columns[0]
            df = df.rename(columns={first_col: "instance_id"})

        # 3) Ensure 'instance_id' is string, then cast to integer index
        #    (some CSVs might have leading zeros or be int already)
        df["instance_id"] = df["instance_id"].astype(str)
        # Keep only rows where instance_id is a digit (drop headers, etc.)
        df = df[df["instance_id"].str.match(r"^\d+$")].copy()
        df["instance_i"] = df["instance_id"].astype(int)

        # 4) Re‐index from 1..num_instances (so we have a row for each even if missing)
        df = df.set_index("instance_i").sort_index()
        idx = pd.RangeIndex(1, num_instances + 1)
        df = df.reindex(idx)

        # 5) Now ensure the expected columns exist: makespan, feasible, runtime
        #    If any of these are missing, create them as NaN.
        for required in ["makespan", "feasible", "runtime"]:
            if required not in df.columns:
                df[required] = np.nan

        dfs[solver] = df[["instance_id", "makespan", "feasible", "runtime"]]
    return dfs


def compute_infeasible_rate(df):
    """
    df: DataFrame with columns [makespan, feasible, runtime];
    feasible==1 means solver produced a finite schedule. NaN or 0 means infeasible.
    Returns fraction of instances where feasible != 1 (i.e. either 0 or NaN).
    """
    total = len(df)
    # treat anything other than exactly 1 as infeasible
    is_feasible = df["feasible"] == 1
    num_feasible = is_feasible.sum()
    num_infeasible = total - num_feasible
    return num_infeasible / float(total)


def compute_avg_runtime(df):
    """
    Compute average runtime (over all instances, treating NaN as 0),
    plus [25th, 50th, 75th, 90th, 99th] percentiles over only the feasible runs.
    """
    runtimes_all = df["runtime"].to_numpy(dtype=float)
    # Replace NaN with 0 for the “average over all”
    runtimes_filled = np.where(np.isnan(runtimes_all), 0.0, runtimes_all)
    avg_all = np.mean(runtimes_filled)

    # Percentiles: only consider runtime where df["feasible"]==1
    feasible_mask = df["feasible"] == 1
    runtimes_feasible = df.loc[feasible_mask, "runtime"].dropna().to_numpy(dtype=float)
    if len(runtimes_feasible) == 0:
        rt_perc = [np.nan] * 5
    else:
        rt_perc = np.percentile(runtimes_feasible, [25, 50, 75, 90, 99])
    return avg_all, rt_perc


def compute_avg_gap(df_solver, df_gurobi):
    """
    Only consider indices where both solver and gurobi have feasible==1.
    Gap(i) = (z_solver(i) - z_opt(i)) / z_opt(i) * 100.
    Return mean gap (over those indices), and the array of all individual gaps.
    """
    mask = (df_solver["feasible"] == 1) & (df_gurobi["feasible"] == 1)
    z_opt = df_gurobi.loc[mask, "makespan"].to_numpy(dtype=float)
    z_sol = df_solver.loc[mask, "makespan"].to_numpy(dtype=float)
    if len(z_opt) == 0:
        return np.nan, np.array([])
    gap = (z_sol - z_opt) / z_opt * 100.0
    return np.mean(gap), gap


def compute_M1_alpha(df_solver, df_gurobi, alphas):
    """
    For each α in alphas, compute fraction of instances i (where z_opt is finite)
    such that solver_makespan(i) ≤ α * z_opt(i).
    Returns a numpy array of length len(alphas) containing those fractions.
    """
    z_opt = df_gurobi["makespan"].to_numpy(dtype=float)
    z_sol = df_solver["makespan"].to_numpy(dtype=float)
    feasible_opt = df_gurobi["feasible"] == 1

    results = []
    for α in alphas:
        valid_idx = np.where(feasible_opt.to_numpy())[0]
        if len(valid_idx) == 0:
            results.append(np.nan)
            continue
        count_ok = np.sum(z_sol[valid_idx] <= α * z_opt[valid_idx])
        results.append(count_ok / float(len(valid_idx)))
    return np.array(results)


def summarize_all(dfs, num_instances, output_folder):
    """
    Given dict of DataFrames {solver → df}, compute and print summary metrics,
    and also save a single CSV summarizing each solver’s aggregated performance.
    """
    os.makedirs(output_folder, exist_ok=True)

    rows = []
    α_values = np.linspace(1.0, 2.0, 101)
    df_gurobi = dfs["gurobi"]

    for solver in SOLVER_NAMES:
        df = dfs[solver]

        infeas = compute_infeasible_rate(df)
        avg_rt, rt_perc = compute_avg_runtime(df)
        avg_gap, gap_array = compute_avg_gap(df, df_gurobi)
        M1 = compute_M1_alpha(df, df_gurobi, α_values)

        rows.append({
            "solver"         : solver,
            "pct_infeasible" : infeas * 100.0,
            "avg_runtime"    : avg_rt,
            "rt_p25"         : rt_perc[0],
            "rt_p50"         : rt_perc[1],
            "rt_p75"         : rt_perc[2],
            "rt_p90"         : rt_perc[3],
            "rt_p99"         : rt_perc[4],
            "avg_gap_pct"    : avg_gap,
            "num_gap_samples": len(gap_array)
        })

        # Save per‐solver M₁ curve
        df_m1 = pd.DataFrame({
            "alpha": α_values,
            "M1_pct": M1 * 100.0
        })
        df_m1.to_csv(os.path.join(output_folder, f"M1_curve_{solver}.csv"), index=False)

        # Also save full gap distribution
        np.savetxt(
            os.path.join(output_folder, f"gaps_{solver}.txt"),
            gap_array,
            header="gap(%) for each (solver, gurobi)-feasible instance",
            fmt="%.6f"
        )

    summary_df = pd.DataFrame(rows)
    summary_df.to_csv(os.path.join(output_folder, "summary_per_solver.csv"), index=False)

    print("\n==== SUMMARY PER SOLVER ====")
    print(summary_df.to_string(index=False, float_format="%.3f"))


def plot_all(dfs, num_instances, output_folder):
    """
    Generates and saves the following plots under output_folder:
      - M₁(α) curves for all solvers (excluding Gurobi)
      - Runtime boxplots (only feasible runs)
      - Gap boxplots (only feasible runs, relative to z_opt)
    """
    os.makedirs(output_folder, exist_ok=True)
    df_gurobi = dfs["gurobi"]
    α_values = np.linspace(1.0, 2.0, 101)

    # 1) M₁ Curves (exclude Gurobi itself)
    plt.figure(figsize=(6, 4))
    for solver in SOLVER_NAMES:
        if solver == "gurobi":
            continue
        M1 = compute_M1_alpha(dfs[solver], df_gurobi, α_values)
        plt.plot(α_values, M1 * 100.0, label=solver.upper())
    plt.xlabel("α")
    plt.ylabel("M₁(α) [%]")
    plt.title("M₁(α) Curves (1.0 ≤ α ≤ 2.0)")
    plt.legend()
    plt.grid(True, lw=0.5, linestyle="--", alpha=0.6)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, "M1_alpha_curves.png"), dpi=200)
    plt.close()

    # 2) Runtime boxplot (only feasible runs)
    plt.figure(figsize=(6, 4))
    data = []
    labels = []
    for solver in SOLVER_NAMES:
        runtimes = dfs[solver]["runtime"]
        mask_feasible = dfs[solver]["feasible"] == 1
        runtimes_feasible = runtimes.loc[mask_feasible].dropna().to_numpy(dtype=float)
        data.append(runtimes_feasible)
        labels.append(solver.upper())
    plt.boxplot(data, labels=labels, showfliers=False, medianprops={"color": "black"})
    plt.ylabel("Runtime (s)")
    plt.title("Runtime (Only Feasible Instances)")
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, "runtime_boxplot.png"), dpi=200)
    plt.close()

    # 3) Gap boxplot (only feasible runs vs Gurobi)
    plt.figure(figsize=(6, 4))
    data = []
    labels = []
    for solver in SOLVER_NAMES:
        if solver == "gurobi":
            continue
        _, gap_arr = compute_avg_gap(dfs[solver], dfs["gurobi"])
        data.append(gap_arr)
        labels.append(solver.upper())
    plt.boxplot(data, labels=labels, showfliers=False, medianprops={"color": "black"})
    plt.ylabel("Optimality Gap (%)")
    plt.title("Gap vs Gurobi (Only Instances Both Feasible)")
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, "gap_boxplot.png"), dpi=200)
    plt.close()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--csv-folder",
        required=True,
        help="Folder containing makespans_{solver}.csv for each solver"
    )
    parser.add_argument(
        "--num-instances",
        type=int,
        required=True,
        help="Total number of instances (e.g. 700 or 1000)"
    )
    parser.add_argument(
        "--output-folder",
        required=True,
        help="Folder to write summary CSV and plots"
    )
    args = parser.parse_args()

    # 1) Load all CSVs in a robust way
    dfs = load_all_csvs(args.csv_folder, args.num_instances)

    # 2) Compute & save summary metrics
    summarize_all(dfs, args.num_instances, args.output_folder)

    # 3) Plot M₁(α), runtime, gap
    plot_all(dfs, args.num_instances, args.output_folder)

    print(f"\nAll metrics & plots saved to: {args.output_folder}")


if __name__ == "__main__":
    main()

