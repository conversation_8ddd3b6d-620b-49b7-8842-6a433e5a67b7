# calculate_makespans.py
# -*- coding: utf-8 -*-
"""
Batch‐compute makespan for each test instance in problem_instances_test.

Directory layout must be:

    problem_instances_test/
        constrains/
            00001_dur.txt
            00001_ddl.txt
            00001_wait.txt
            00001_loc.txt
            00002_dur.txt
            00002_ddl.txt
            00002_wait.txt
            00002_loc.txt
            … (etc.)
        solutions/
            00001_w.txt
            00001_0.txt
            00001_1.txt
            00002_w.txt
            00002_0.txt
            00002_1.txt
            … (per‐robot files + global order _w.txt)
"""

import os
import sys
import glob
import time
import numpy as np

# Import SchedulingEnv from your utils.py
from utils import SchedulingEnv

def find_instance_ids(problem_folder):
    """
    Find all files matching '*_dur.txt' inside `problem_folder` and
    return a sorted list of instance IDs (strings), e.g. ['00001','00002', …].
    """
    pattern = os.path.join(problem_folder, "*_dur.txt")
    dur_files = glob.glob(pattern)
    instance_ids = []
    for fp in dur_files:
        fname = os.path.basename(fp)
        if fname.endswith("_dur.txt"):
            instance_id = fname[:-len("_dur.txt")]
            instance_ids.append(instance_id)
    try:
        instance_ids = sorted(instance_ids, key=lambda x: int(x))
    except ValueError:
        instance_ids = sorted(instance_ids)
    return instance_ids

def load_solution_files(solution_folder, instance_id, num_robots):
    """
    Load the global order (instance_id + '_w.txt') and each per‐robot file
    (instance_id + '_0.txt', '_1.txt', … up to num_robots).
    Returns:
      - global_order: list[int]
      - optimals: List[List[int]] of length = num_robots
      - missing_flag: True if any required file is missing
    """
    missing_flag = False
    w_path = os.path.join(solution_folder, f"{instance_id}_w.txt")
    if not os.path.isfile(w_path):
        print(f"    [ERROR] Missing global‐order file: {w_path}")
        return None, None, True

    global_order = np.loadtxt(w_path, dtype=np.int32).flatten().tolist()

    optimals = []
    for r in range(num_robots):
        robot_path = os.path.join(solution_folder, f"{instance_id}_{r}.txt")
        if os.path.isfile(robot_path):
            arr = np.loadtxt(robot_path, dtype=np.int32).flatten().tolist()
            optimals.append(arr)
        else:
            # If a robot‐r file is absent, we treat it as “no tasks” for that robot.
            print(f"    [WARN] Missing file for robot {r}: {robot_path} → using empty list")
            optimals.append([])

    return global_order, optimals, False

def compute_makespan_for_instance(problem_folder, solution_folder, instance_id):
    """
    1) Load constraints from problem_folder/<instance_id>_[dur|ddl|wait|loc].txt
    2) Instantiate SchedulingEnv
    3) Load solution (global w.txt + per‐robot lists) from solution_folder
    4) Re‐instantiate SchedulingEnv (fresh)
    5) For each task in global_order:
         - figure out which robot to assign
         - call env.insert_robot(task_id, robot_id)
         - if any insertion fails, return (False, None)
    6) At the end: env.min_makespan holds the final makespan. Return (True, makespan).
    """
    # 1) Paths to constraint files
    dur_path  = os.path.join(problem_folder, f"{instance_id}_dur.txt")
    ddl_path  = os.path.join(problem_folder, f"{instance_id}_ddl.txt")
    wait_path = os.path.join(problem_folder, f"{instance_id}_wait.txt")
    loc_path  = os.path.join(problem_folder, f"{instance_id}_loc.txt")

    # Verify they all exist
    for p in (dur_path, ddl_path, wait_path, loc_path):
        if not os.path.isfile(p):
            print(f"  [ERROR] Missing constraint file: {p}")
            return False, None

    # 2) One‐time init to read num_robots
    env_pre = SchedulingEnv(os.path.join(problem_folder, instance_id))

    # 3) Load solutions:
    global_order, optimals, missing = load_solution_files(solution_folder, instance_id, env_pre.num_robots)
    if missing:
        return False, None

    # 4) Fresh SchedulingEnv for actual simulation
    env = SchedulingEnv(os.path.join(problem_folder, instance_id))

    # 5) Step through global_order
    for step_index, task_id in enumerate(global_order):
        # Determine which robot “owns” this task
        assigned_robot = None
        for r, tasks_r in enumerate(optimals):
            if task_id in tasks_r:
                assigned_robot = r
                break
        if assigned_robot is None:
            # If not found in any per‐robot file, default to robot 0
            assigned_robot = 0

        success, reward, done = env.insert_robot(task_id, assigned_robot)
        if not success:
            print(f"    [Instance {instance_id}] → Infeasible when inserting task {task_id} "
                  f"to robot {assigned_robot} (step {step_index+1}/{len(global_order)})")
            return False, None

        # If done=True but tasks remain, that’s also a failure
        if done and (step_index < len(global_order) - 1):
            print(f"    [Instance {instance_id}] → Reached done=True prematurely at step {step_index+1}")
            return False, None

    # 6) If we reached here, all insertions succeeded. Final makespan = env.min_makespan
    return True, env.min_makespan

def main():
    # ------------------------------
    # 0) Configuration: folders
    # ------------------------------
    PROBLEM_FOLDER  = os.path.join("problem_test", "constraints")
    SOLUTION_FOLDER = os.path.join("problem_test", "solutions")

    if not os.path.isdir(PROBLEM_FOLDER):
        print(f"ERROR: Problem‐instances folder not found: {PROBLEM_FOLDER}")
        sys.exit(1)
    if not os.path.isdir(SOLUTION_FOLDER):
        print(f"ERROR: Solutions folder not found: {SOLUTION_FOLDER}")
        sys.exit(1)

    # ------------------------------
    # 1) Discover all instance IDs
    # ------------------------------
    inst_ids = find_instance_ids(PROBLEM_FOLDER)
    if len(inst_ids) == 0:
        print("No '*_dur.txt' files found in:", PROBLEM_FOLDER, "\nExiting.")
        sys.exit(1)

    print(f"Found {len(inst_ids)} instance(s). Calculating makespan for each...\n")

    # ------------------------------
    # 2) Loop over each instance
    # ------------------------------
    start_time = time.time()
    num_passed = 0
    num_failed = 0

    # We'll also collect (id, makespan) tuples for reporting
    results = []

    for count, inst_id in enumerate(inst_ids, start=1):
        print(f"[{count:4d}/{len(inst_ids)}] Instance = {inst_id} …", end=" ", flush=True)
        ok, makespan = compute_makespan_for_instance(PROBLEM_FOLDER, SOLUTION_FOLDER, inst_id)
        if ok:
            print(f"MAKESPAN = {makespan:.3f}")
            num_passed += 1
            results.append((inst_id, makespan))
        else:
            print("FAIL")
            num_failed += 1

    elapsed = time.time() - start_time
    print("\n=== ALL INSTANCES PROCESSED ===")
    print(f"  Total instances: {len(inst_ids)}")
    print(f"  Succeeded:       {num_passed}")
    print(f"  Failed:          {num_failed}")
    print(f"  Elapsed time:    {elapsed:.1f} seconds")

    # (Optional) Write results to a CSV for later analysis
    out_csv = "makespan_results.csv"
    with open(out_csv, "w") as f:
        f.write("InstanceID,Makespan\n")
        for inst_id, ms in results:
            f.write(f"{inst_id},{ms:.6f}\n")
    print(f"\nSaved makespans to '{out_csv}'")

if __name__ == "__main__":
    main()

