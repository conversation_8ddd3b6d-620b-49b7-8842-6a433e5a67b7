




# -*- coding: utf-8 -*-
"""
Created on Fri Nov  8 13:47:42 2019

@author: pheno

Supervised training
"""

import copy
import os
import pickle
import time
import argparse

import numpy as np
import torch
import torch.nn.functional as F
import torch.nn.utils as utils
from torch.optim.lr_scheduler import ReduceLROnPlateau
import matplotlib.pyplot as plt

from hetnet import ScheduleNet4Layer
from utils import ReplayMemory, Transition, action_helper_rollout
from utils import SchedulingEnv, hetgraph_node_helper, build_hetgraph

class RewardStabilizer:
    """Class to stabilize and smooth reward values during training"""

    def __init__(self, window_size=50, clip_percentile=95):
        self.window_size = window_size
        self.clip_percentile = clip_percentile
        self.reward_buffer = []
        self.running_mean = 0.0
        self.running_std = 1.0
        self.alpha = 0.99  # Exponential moving average factor

    def update_statistics(self, rewards):
        """Update running statistics for reward normalization"""
        batch_mean = np.mean(rewards)
        batch_std = np.std(rewards) + 1e-8  # Add small epsilon to avoid division by zero

        # Update running statistics using exponential moving average
        self.running_mean = self.alpha * self.running_mean + (1 - self.alpha) * batch_mean
        self.running_std = self.alpha * self.running_std + (1 - self.alpha) * batch_std

        # Keep buffer for percentile clipping
        self.reward_buffer.extend(rewards)
        if len(self.reward_buffer) > self.window_size:
            self.reward_buffer = self.reward_buffer[-self.window_size:]

    def normalize_rewards(self, rewards):
        """Normalize rewards using running statistics"""
        # Z-score normalization
        normalized = [(r - self.running_mean) / self.running_std for r in rewards]

        # Clip extreme values using percentile
        if len(self.reward_buffer) > 10:
            clip_value = np.percentile(np.abs(self.reward_buffer), self.clip_percentile)
            normalized = [np.clip(r, -clip_value, clip_value) for r in normalized]

        return normalized

    def get_smoothed_reward(self, rewards):
        """Get smoothed and normalized rewards"""
        self.update_statistics(rewards)
        return self.normalize_rewards(rewards)

'''
Fill memory buffer with demonstration data set for 5 robots and 30 tasks
    use constraints/solutions folder structure
'''
def fill_demo_data_5r_30t(constraints_folder, solutions_folder, start_no, end_no, gamma_d):
    memory = ReplayMemory(1000*20)

    total_no = end_no - start_no + 1
    gurobi_count = 0

    for graph_no in range(start_no, end_no+1):
        print('Loading.. {}/{}'.format(graph_no, total_no), end='\r')

        # Load from constraints folder
        fname = os.path.join(constraints_folder, f'{graph_no:05d}')
        if not os.path.isfile(fname + '_dur.txt'):
            continue

        env = SchedulingEnv(fname)

        # Load from solutions folder
        solutions_prefix = os.path.join(solutions_folder, f'{graph_no:05d}')
        solname_w = solutions_prefix + '_w.txt'

        if os.path.isfile(solname_w):
            gurobi_count += 1

            optimals = []
            for i in range(env.num_robots):
                robot_file = solutions_prefix + f'_{i}.txt'
                if os.path.isfile(robot_file):
                    optimals.append(np.loadtxt(robot_file, dtype=np.int32))
                else:
                    optimals.append([])

            optimalw = np.loadtxt(solname_w, dtype=np.int32)
        else:
            continue

        # Generate transitions of the problem
        state_graphs = []
        partials = []
        partialw = []
        actions_task = []
        actions_robot = []
        rewards = []
        terminates = []

        state_graphs.append(copy.deepcopy(env.halfDG))
        partials.append(copy.deepcopy(env.partials))
        partialw.append(copy.deepcopy(env.partialw))
        terminates.append(False)

        rj = 0
        for i in range(env.num_tasks):
            for j in range(env.num_robots):
                if optimalw[i] in optimals[j]:
                    rj = j
                    break

            act_chosen = optimalw[i]

            # Insert the node, update state, and get reward
            _, reward, done = env.insert_robot(act_chosen, rj)

            state_graphs.append(copy.deepcopy(env.halfDG))
            partials.append(copy.deepcopy(env.partials))
            partialw.append(copy.deepcopy(env.partialw))
            actions_task.append(act_chosen)
            actions_robot.append(rj)
            rewards.append(reward)
            terminates.append(done)

        # Save transitions into memory buffer
        for t in range(env.num_tasks):
            curr_g = copy.deepcopy(state_graphs[t])
            curr_partials = copy.deepcopy(partials[t])
            curr_partialw = copy.deepcopy(partialw[t])
            act_task = actions_task[t]
            act_robot = actions_robot[t]

            # Calculate discounted reward
            reward_n = 0.0
            for j in range(t, env.num_tasks):
                reward_n += (gamma_d**(j-t)) * rewards[j]
            next_g = copy.deepcopy(state_graphs[t+1])
            next_partials = copy.deepcopy(partials[t+1])
            next_partialw = copy.deepcopy(partialw[t+1])
            next_done = terminates[t+1]

            locs = copy.deepcopy(env.loc)
            durs = copy.deepcopy(env.dur)

            memory.push(curr_g, curr_partials, curr_partialw,
                        locs, durs,
                        act_task, act_robot,
                        reward_n, next_g, next_partials,
                        next_partialw, next_done)

    print('Gurobi feasible found: {}/{}'.format(gurobi_count, total_no))
    print('Memory buffer size: {}'.format(len(memory)))
    return memory

'''
Original fill_demo_data function (kept for backward compatibility)
'''
def fill_demo_data(folder, start_no, end_no, gamma_d):
    memory = ReplayMemory(1000*20)

    total_no = end_no - start_no + 1
    gurobi_count = 0
    
    
    for graph_no in range(start_no, end_no+1):
        print('Loading.. {}/{}'.format(graph_no, total_no), end='\r')
        fname = folder + '/%05d' % graph_no
        env = SchedulingEnv(fname)

        # check if the graph is feasible for Gurobi
        solname = folder + 'v9/%05d' % graph_no
        solname_w = solname +'_w.txt'
        
        if os.path.isfile(solname_w):
            gurobi_count += 1
            
            optimals = []
            for i in range(env.num_robots):
                if os.path.isfile(solname+'_%d.txt' % i):
                    optimals.append(np.loadtxt(solname+'_%d.txt' % i, dtype=np.int32))
                else:
                    optimals.append([])
                
            optimalw = np.loadtxt(solname_w, dtype=np.int32)
        else:
            continue
    
        '''
        generate transitions of the problem
        '''
        state_graphs = []
        partials = []
        partialw = []
        actions_task = []
        actions_robot = []
        rewards = []
        terminates = []
        
        #print("Checking SchedulingEnv attributes:", dir(env))
        
       # print("EEEEEEEEEEEEEEEEEEEE", env.halfDG)

        
        state_graphs.append(copy.deepcopy(env.halfDG))
        partials.append(copy.deepcopy(env.partials))
        partialw.append(copy.deepcopy(env.partialw))
        terminates.append(False)
        rj=0
        for i in range(env.num_tasks):
            for j in range(env.num_robots):
                if optimalw[i] in optimals[j]:
                    rj = j
                    break
            
            act_chosen = optimalw[i]
            #print('step: %d, action: [%d, %d]' % (t, act_chosen, rj))
            
            # insert the node, update state, and get reward
            _, reward, done = env.insert_robot(act_chosen, rj)
            #print(rt, reward, done, env.min_makespan)
            
            print(env.halfDG)
            state_graphs.append(copy.deepcopy(env.halfDG))
            partials.append(copy.deepcopy(env.partials))
            partialw.append(copy.deepcopy(env.partialw))
            actions_task.append(act_chosen)
            actions_robot.append(rj)
            rewards.append(reward)
            terminates.append(done)
    
        '''
        save transitions into memory buffer
        '''
        for t in range(env.num_tasks):
            curr_g = copy.deepcopy(state_graphs[t])
            curr_partials = copy.deepcopy(partials[t])
            curr_partialw = copy.deepcopy(partialw[t])
            act_task = actions_task[t]
            act_robot = actions_robot[t]
            # calculate discounted reward
            reward_n = 0.0
            for j in range(t, env.num_tasks):
                reward_n += (gamma_d**(j-t)) * rewards[j]
            next_g = copy.deepcopy(state_graphs[t+1])
            next_partials = copy.deepcopy(partials[t+1])
            next_partialw = copy.deepcopy(partialw[t+1])
            next_done = terminates[t+1]
            
            locs = copy.deepcopy(env.loc)
            durs = copy.deepcopy(env.dur)
            
            memory.push(curr_g, curr_partials, curr_partialw,
                        locs, durs,
                        act_task, act_robot,
                        reward_n, next_g, next_partials, 
                        next_partialw, next_done)
    
    print('Gurobi feasible found: {}/{}'.format(gurobi_count, total_no))
    print('Memory buffer size: {}'.format(len(memory)))
    return memory
    
if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--cpu', default=False, action='store_true')
    parser.add_argument('--path-to-train', default='./problem_instances_5r_30t/constraints', type=str)
    parser.add_argument('--path-to-solutions', default='./problem_instances_5r_30t/solutions', type=str)
    parser.add_argument('--num-robots', default=5, type=int)
    parser.add_argument('--train-start-no', default=1, type=int)
    parser.add_argument('--train-end-no', default=1000, type=int)
    parser.add_argument('--steps', default=1000,type=int)
    parser.add_argument('--gamma', default=0.99, type=float)
    parser.add_argument('--batch-size', default=8, type=int)
    parser.add_argument('--lr', default=1e-4, type=float)
    parser.add_argument('--weight-decay', default=1e-6, type=float)
    parser.add_argument('--resume-training', default=False, action='store_true')
    parser.add_argument('--path-to-checkpoint', default='./sltrain02/checkpoint_07000.tar', type=str)
    parser.add_argument('--load-memory', default=False, action='store_true')
    parser.add_argument('--path-to-replay-buffer', default='./buffer/buffer_half_C3.pkl', type=str)
    parser.add_argument('--checkpoint-interval', default=10, type=int)
    parser.add_argument('--save-replay-buffer-to', default=None, type=str)
    parser.add_argument('--cpsave', default='./cp', type=str)
    args = parser.parse_args()

    resume_training = args.resume_training
    load_memory = args.load_memory
        
    GAMMA = args.gamma
    BATCH_SIZE = args.batch_size
    total_steps = args.steps
    
    loss_history = []
    q_pred_history = []
    q_target_history = []
    reward_history = []
    reward_running_mean = []
    reward_running_std = []
    device = torch.device("cpu")

    #device = torch.device("cpu") if args.cpu else torch.device("cuda")

    # Network dimensions optimized for 5 robots and 30 tasks
    in_dim = {'task': 6,
              'loc': 1,
              'robot': 1,
              'state': 4,
              'value': 1
              }

    hid_dim = {'task': 128,  # Increased for 5 robots and 30 tasks complexity
               'loc': 64,
               'robot': 64,
               'state': 64,
               'value': 128   # Increased for better value estimation
               }

    out_dim = {'task': 64,   # Increased for 5 robots and 30 tasks
               'loc': 32,
               'robot': 32,
               'state': 32,
               'value': 1
               }

    cetypes = [('task', 'temporal', 'task'),
               ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
               ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
               ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
               ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
               ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
               ('state', 'sto', 'value'), ('value', 'vto', 'value'),
               ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')]
    
    num_heads = 8
    num_robots = args.num_robots
    map_width = 6
    loc_dist_threshold = 1
    
    policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, num_heads).to(device)

    # Use Adam optimizer with improved learning rate
    optimizer = torch.optim.Adam(policy_net.parameters(), lr=args.lr, weight_decay=args.weight_decay)

    # More aggressive learning rate scheduling with smaller patience
    lr_scheduler = ReduceLROnPlateau(optimizer, 'min', factor=0.3, patience=20, min_lr=1e-7)

    
    if resume_training:
        trained_checkpoint = args.path_to_checkpoint
        cp = torch.load(trained_checkpoint)
        policy_net.load_state_dict(cp['policy_net_state_dict'])
        #target_net.load_state_dict(cp['target_net_state_dict'])
        optimizer.load_state_dict(cp['optimizer_state_dict'])
        training_steps_done = cp['training_steps']
        start_step = training_steps_done + 1
    else:
        start_step = 1
        training_steps_done = 0
    
    if load_memory:
        # load replay buffer
        bname = args.path_to_replay_buffer
        with open(bname, 'rb') as f: # open file with read-mode  
            memory = pickle.load(f) # serialize and save object
        print('Memory loaded, length: %d' % len(memory))
    else:
        constraints_folder = args.path_to_train
        solutions_folder = args.path_to_solutions
        start_no = args.train_start_no
        end_no = args.train_end_no
        memory = fill_demo_data_5r_30t(constraints_folder, solutions_folder, start_no, end_no, GAMMA)
    
    print('Initialization done')

    # Ensure checkpoint directory exists
    os.makedirs(args.cpsave, exist_ok=True)
    print(f'Checkpoints will be saved to: {args.cpsave}')

    '''
    Training phase
    '''
    # Initialize reward stabilizer
    reward_stabilizer = RewardStabilizer(window_size=100, clip_percentile=90)

    print('Starting training with improved stability...')
    print(f'Learning rate: {args.lr}, Batch size: {BATCH_SIZE}')
    print('Reward stabilization: ENABLED (Z-score normalization + percentile clipping)')

    for i_step in range(start_step, total_steps+1):
        start_t = time.time()
        policy_net.train()
        print('training no. %d' % i_step)

        transitions = memory.sample(BATCH_SIZE)
        batch = Transition(*zip(*transitions))
        loss = torch.tensor(0.0).to(device)

        # Track Q-values and targets for logging
        batch_q_preds = []
        batch_q_targets = []
        batch_rewards_raw = []  # Raw rewards before stabilization
        batch_rewards_stabilized = []  # Stabilized rewards

        # First pass: collect all raw rewards for stabilization
        for i in range(BATCH_SIZE):
            raw_reward = batch.reward_n[i]
            batch_rewards_raw.append(raw_reward)

        # Apply reward stabilization to entire batch
        batch_rewards_stabilized = reward_stabilizer.get_smoothed_reward(batch_rewards_raw)

        # Second pass: process samples with stabilized rewards
        for i in range(BATCH_SIZE):

            num_tasks = batch.curr_g[i].number_of_nodes() - 2
            unsch_tasks = np.array(action_helper_rollout(num_tasks, batch.curr_partialw[i]),
                                   dtype=np.int64)

            g = build_hetgraph(batch.curr_g[i], num_tasks, num_robots, batch.durs[i],
                               map_width, np.array(batch.locs[i], dtype=np.int64),
                               loc_dist_threshold, batch.curr_partials[i], unsch_tasks,
                               batch.act_robot[i], unsch_tasks)
            g = g.to(device)

            num_actions = len(unsch_tasks)
            feat_dict = hetgraph_node_helper(batch.curr_g[i].number_of_nodes(),
                                             batch.curr_partialw[i],
                                             batch.curr_partials[i],
                                             batch.locs[i], batch.durs[i],
                                             map_width, num_robots, num_actions)

            feat_dict_tensor = {}
            for key in feat_dict:
                feat_dict_tensor[key] = torch.Tensor(feat_dict[key]).to(device)

            outputs = policy_net(g, feat_dict_tensor)
            q_pre = outputs['value']

            # Use stabilized reward with additional normalization
            stabilized_reward = batch_rewards_stabilized[i]
            normalized_reward = stabilized_reward / 100.0  # Additional scaling

            '''
            Calculate TD loss & LfD loss with stabilized rewards
            '''
            if num_actions > 1:
                # Much smaller offset for better convergence
                offset = 0.01  # Even smaller offset
                target_list = np.full((num_actions, 1),
                                      normalized_reward - offset, dtype=np.float32)

                LfD_weights = np.full((num_actions, 1),
                                      0.8/(num_actions-1), dtype=np.float32)

                q_s_a_alt_target1 = q_pre.clone().detach()
                q_s_a_alt_target2 = torch.tensor(target_list).to(device)
                q_s_a_alt_target = torch.min(q_s_a_alt_target1, q_s_a_alt_target2)

                # q value for expert action
                expert_idx = 0
                for j in range(num_actions):
                    if unsch_tasks[j] == batch.act_task[i]:
                        expert_idx = j
                        break
                q_s_a_alt_target[expert_idx, 0] = normalized_reward
                LfD_weights[expert_idx, 0] = 1.0
            else:
                # num_actions == 1
                target_list = np.full((1, 1), normalized_reward, dtype=np.float32)
                LfD_weights = np.full((1, 1), 1.0, dtype=np.float32)
                q_s_a_alt_target = torch.tensor(target_list).to(device)

            # Store predictions and targets for logging
            batch_q_preds.append(q_pre.detach().cpu().numpy())
            batch_q_targets.append(q_s_a_alt_target.detach().cpu().numpy())

            loss_SL = F.mse_loss(q_pre, q_s_a_alt_target, reduction='none')
            LfD_weights = torch.Tensor(LfD_weights).to(device)
            loss_SL = loss_SL * LfD_weights
            loss += loss_SL.sum() / BATCH_SIZE

        loss_batch = loss.data.cpu().numpy()

        # Apply more aggressive gradient clipping for stability
        optimizer.zero_grad()
        loss.backward()
        utils.clip_grad_norm_(policy_net.parameters(), max_norm=0.5)
        optimizer.step()

        # Update learning rate scheduler
        if i_step > 1:
            lr_scheduler.step(loss_batch)

        # Calculate prediction differences for monitoring
        avg_q_pred = np.mean([np.mean(q) for q in batch_q_preds])
        avg_q_target = np.mean([np.mean(q) for q in batch_q_targets])
        avg_reward_raw = np.mean(batch_rewards_raw)
        avg_reward_stabilized = np.mean(batch_rewards_stabilized)
        pred_diff = abs(avg_q_pred - avg_q_target)

        # Store metrics
        loss_history.append(loss_batch)
        q_pred_history.append(avg_q_pred)
        q_target_history.append(avg_q_target)
        reward_history.append(avg_reward_stabilized)  # Use stabilized rewards for history
        reward_running_mean.append(reward_stabilizer.running_mean)
        reward_running_std.append(reward_stabilizer.running_std)

        end_t = time.time()
        current_lr = optimizer.param_groups[0]['lr']

        print('[step {}] Loss {:.6f}, Q_pred {:.4f}, Q_target {:.4f}, Diff {:.4f}, R_raw {:.3f}, R_stab {:.3f}, LR {:.2e}, time: {:.4f} s'
              .format(i_step, loss_batch, avg_q_pred, avg_q_target, pred_diff, avg_reward_raw, avg_reward_stabilized, current_lr, end_t - start_t))

        # Early stopping if loss is very low (adjusted for new normalization)
        if loss_batch < 0.001:
            print(f'Loss below 0.001 achieved at step {i_step}! Stopping training.')
            break

        # Additional stability check - if loss explodes, reduce learning rate
        if loss_batch > 1000 and i_step > 10:
            print(f'Loss explosion detected ({loss_batch:.2f}), reducing learning rate by half')
            for param_group in optimizer.param_groups:
                param_group['lr'] *= 0.5

        '''
        Save checkpoints
        '''
        if i_step % args.checkpoint_interval == 0:
            checkpoint_path = args.cpsave + '/checkpoint_{:05d}.tar'.format(i_step)
            torch.save({
                'training_steps': i_step,
                'policy_net_state_dict': policy_net.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': loss_history,
                'q_pred_history': q_pred_history,
                'q_target_history': q_target_history,
                'reward_history': reward_history,
                'reward_running_mean': reward_running_mean,
                'reward_running_std': reward_running_std,
                'reward_stabilizer_state': {
                    'running_mean': reward_stabilizer.running_mean,
                    'running_std': reward_stabilizer.running_std,
                    'reward_buffer': reward_stabilizer.reward_buffer
                }
            }, checkpoint_path)
            print('checkpoint saved')

    # Plot training curves
    print('Plotting training curves...')
    plt.figure(figsize=(15, 10))

    # Loss curve
    plt.subplot(2, 3, 1)
    plt.plot(loss_history)
    plt.title('Training Loss')
    plt.xlabel('Training Step')
    plt.ylabel('Loss')
    plt.yscale('log')
    plt.grid(True)

    # Q-value predictions vs targets
    plt.subplot(2, 3, 2)
    plt.plot(q_pred_history, label='Q Predictions', alpha=0.7)
    plt.plot(q_target_history, label='Q Targets', alpha=0.7)
    plt.title('Q-Values: Predictions vs Targets')
    plt.xlabel('Training Step')
    plt.ylabel('Q-Value')
    plt.legend()
    plt.grid(True)

    # Prediction difference
    plt.subplot(2, 3, 3)
    pred_diffs = [abs(p - t) for p, t in zip(q_pred_history, q_target_history)]
    plt.plot(pred_diffs)
    plt.title('|Q_pred - Q_target|')
    plt.xlabel('Training Step')
    plt.ylabel('Absolute Difference')
    plt.yscale('log')
    plt.grid(True)

    # Reward stabilization
    plt.subplot(2, 3, 4)
    plt.plot(reward_history, label='Stabilized Rewards', alpha=0.8)
    plt.plot(reward_running_mean, label='Running Mean', linestyle='--', alpha=0.7)
    plt.fill_between(range(len(reward_running_mean)),
                     np.array(reward_running_mean) - np.array(reward_running_std),
                     np.array(reward_running_mean) + np.array(reward_running_std),
                     alpha=0.2, label='±1 Std')
    plt.title('Reward Stabilization')
    plt.xlabel('Training Step')
    plt.ylabel('Reward')
    plt.legend()
    plt.grid(True)

    # Loss histogram
    plt.subplot(2, 3, 5)
    plt.hist(loss_history, bins=50, alpha=0.7)
    plt.title('Loss Distribution')
    plt.xlabel('Loss')
    plt.ylabel('Frequency')
    plt.grid(True)

    # Final loss trend (last 50 steps)
    plt.subplot(2, 3, 6)
    if len(loss_history) > 50:
        plt.plot(loss_history[-50:])
        plt.title('Loss Trend (Last 50 Steps)')
    else:
        plt.plot(loss_history)
        plt.title('Loss Trend (All Steps)')
    plt.xlabel('Training Step')
    plt.ylabel('Loss')
    plt.grid(True)

    plt.tight_layout()
    plt.savefig('training_curves.png', dpi=300, bbox_inches='tight')
    plt.show()

    print(f'Final loss: {loss_history[-1]:.6f}')
    print(f'Minimum loss achieved: {min(loss_history):.6f}')
    print(f'Final Q prediction difference: {pred_diffs[-1]:.6f}')

    # save replay buffer
    if args.save_replay_buffer_to is not None:
        with open(args.save_replay_buffer_to, 'wb') as f:  # open file with write-mode
            pickle.dump(memory, f)  # serialize and save object
