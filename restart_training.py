#!/usr/bin/env python3
"""
Script to restart training with much more stable parameters
"""

import subprocess
import sys
import os

def restart_training():
    """Restart training with ultra-stable parameters"""
    
    # Create checkpoint directory if it doesn't exist
    checkpoint_dir = "./cp"
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    # Ultra-stable training parameters
    cmd = [
        sys.executable, "lr_scheduler_train.py",
        "--path-to-train", "./gen/r2t20_001",
        "--num-robots", "2",
        "--train-start-no", "1",
        "--train-end-no", "1000",  # Even smaller dataset for stability
        "--steps", "500",  # Fewer steps to test stability first
        "--gamma", "0.99",
        "--batch-size", "8",
        "--lr", "1e-4",  # Much more conservative
        "--weight-decay", "1e-6",
        "--checkpoint-interval", "10",
        "--cpsave", checkpoint_dir
    ]
    
    print("🔄 RESTARTING TRAINING WITH ULTRA-STABLE PARAMETERS")
    print("=" * 60)
    print("Key changes from previous run:")
    print("  ✓ Learning rate: 1e-4 (reduced from 5e-4)")
    print("  ✓ Reward stabilization: Z-score normalization + percentile clipping")
    print("  ✓ Offset: 0.01 (much smaller)")
    print("  ✓ Gradient clipping: 0.5 (was 1.0)")
    print("  ✓ LR scheduler patience: 20 (was 50)")
    print("  ✓ Loss explosion protection: enabled")
    print("  ✓ Early stopping: 0.001 (adjusted for new scale)")
    print("  ✓ Smaller dataset: 50 samples (for initial testing)")
    print("  ✓ Reward smoothing: Running mean/std tracking")
    print("=" * 60)
    print()
    
    try:
        subprocess.run(cmd, check=True)
        print("\n✅ Training completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Training failed with error code {e.returncode}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ Training interrupted by user")
        return False

if __name__ == "__main__":
    print("🚀 MRC SCHEDULING - ULTRA-STABLE TRAINING RESTART")
    print()
    
    # Ask user if they want to proceed
    response = input("This will start training with much more conservative parameters. Continue? (y/n): ")
    if response.lower() not in ['y', 'yes']:
        print("Training cancelled.")
        sys.exit(0)
    
    success = restart_training()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 TRAINING COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("Next steps:")
        print("  1. Check 'training_curves.png' for loss visualization")
        print("  2. Look for gradual loss decrease in the plots")
        print("  3. If stable, increase training steps and dataset size")
        print("  4. Target loss should now be below 0.001 (due to /1000 normalization)")
    else:
        print("\n" + "=" * 60)
        print("❌ TRAINING STILL HAVING ISSUES")
        print("=" * 60)
        print("If training is still unstable, try:")
        print("  • Further reduce learning rate to 5e-5")
        print("  • Increase reward normalization to /10000")
        print("  • Use even smaller batch size (4 instead of 8)")
        print("  • Check if training data is corrupted")
